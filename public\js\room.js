// 房间页面逻辑
class RoomManager {
    constructor() {
        this.roomId = Utils.getPathParameter();
        this.nickname = Utils.storage.get('nickname', '匿名玩家');
        this.players = [];
        this.isHost = false;
        this.isReady = false;
        this.currentTeam = 1;
        this.gameSettings = {
            gameMode: 0,
            gameSpeed: 1.0,
            maxPlayers: 8
        };

        this.initializeElements();
        this.setupEventListeners();
        this.setupWebSocketHandlers();
        this.joinRoom();
    }

    // 初始化DOM元素
    initializeElements() {
        this.elements = {
            roomId: document.getElementById('roomId'),
            leaveRoomBtn: document.getElementById('leaveRoomBtn'),
            gameMode: document.getElementById('gameMode'),
            gameSpeed: document.getElementById('gameSpeed'),
            maxPlayers: document.getElementById('maxPlayers'),
            playersList: document.getElementById('playersList'),
            teamsList: document.getElementById('teamsList'),
            readyBtn: document.getElementById('readyBtn'),
            readyStatus: document.getElementById('readyStatus'),
            startGameBtn: document.getElementById('startGameBtn'),
            chatMessages: document.getElementById('chatMessages'),
            chatInput: document.getElementById('chatInput'),
            sendChatBtn: document.getElementById('sendChatBtn'),
            statusMessage: document.getElementById('statusMessage')
        };

        // 设置房间号显示
        this.elements.roomId.textContent = this.roomId;
    }

    // 设置事件监听器
    setupEventListeners() {
        // 离开房间
        this.elements.leaveRoomBtn.addEventListener('click', () => {
            this.leaveRoom();
        });

        // 游戏设置变更
        this.elements.gameMode.addEventListener('change', () => {
            this.updateGameSettings();
        });

        this.elements.gameSpeed.addEventListener('change', () => {
            this.updateGameSettings();
        });

        this.elements.maxPlayers.addEventListener('change', () => {
            this.updateGameSettings();
        });

        // 准备按钮
        this.elements.readyBtn.addEventListener('click', () => {
            this.toggleReady();
        });

        // 开始游戏按钮
        this.elements.startGameBtn.addEventListener('click', () => {
            this.startGame();
        });

        // 聊天功能
        this.elements.sendChatBtn.addEventListener('click', () => {
            this.sendChat();
        });

        this.elements.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChat();
            }
        });
    }

    // 设置WebSocket消息处理器
    setupWebSocketHandlers() {
        // 加入房间结果
        wsManager.on('JoinResult', (data) => {
            if (!data.success) {
                Utils.showError(data.error || '加入房间失败');
                Utils.navigateTo('/lobby');
            }
        });

        // 房间状态更新
        wsManager.on('RoomStatus', (data) => {
            this.updateRoomStatus(data);
        });

        // 玩家离开
        wsManager.on('PlayerLeft', (data) => {
            this.addChatMessage('系统', `${data.nickname} 离开了房间`);
        });

        // 聊天消息
        wsManager.on('Chat', (data) => {
            this.addChatMessage(data.nickname, data.message);
        });

        // 游戏开始
        wsManager.on('GameStart', (data) => {
            Utils.showSuccess('游戏开始！');
            Utils.navigateTo(`/game/${this.roomId}`);
        });
    }

    // 加入房间
    joinRoom() {
        if (!wsManager.getConnectionStatus()) {
            Utils.showError('未连接到服务器');
            Utils.navigateTo('/lobby');
            return;
        }

        wsManager.send('JoinRoom', {
            roomId: this.roomId,
            nickname: this.nickname
        });
    }

    // 离开房间
    leaveRoom() {
        Utils.navigateTo('/lobby');
    }

    // 更新游戏设置
    updateGameSettings() {
        if (!this.isHost) {
            return;
        }

        this.gameSettings = {
            gameMode: parseInt(this.elements.gameMode.value),
            gameSpeed: parseFloat(this.elements.gameSpeed.value),
            maxPlayers: parseInt(this.elements.maxPlayers.value)
        };

        wsManager.send('UpdateSettings', this.gameSettings);
    }

    // 切换准备状态
    toggleReady() {
        wsManager.send('Ready');
    }

    // 开始游戏
    startGame() {
        if (!this.isHost) {
            return;
        }

        wsManager.send('StartGame');
    }

    // 更改队伍
    changeTeam(teamId) {
        if (this.currentTeam === teamId) {
            return;
        }

        wsManager.send('ChangeTeam', { team: teamId });
    }

    // 发送聊天消息
    sendChat() {
        const message = this.elements.chatInput.value.trim();
        if (!message) {
            return;
        }

        wsManager.send('Chat', { message });
        this.elements.chatInput.value = '';
    }

    // 添加聊天消息
    addChatMessage(nickname, message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message';
        messageElement.innerHTML = `
            <span class="chat-nickname">${nickname}:</span>
            <span class="chat-text">${message}</span>
        `;

        this.elements.chatMessages.appendChild(messageElement);
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }

    // 更新房间状态
    updateRoomStatus(data) {
        this.players = data.players || [];
        this.gameSettings = data.settings || this.gameSettings;

        // 找到当前玩家
        const currentPlayer = this.players.find(p => p.nickname === this.nickname);
        if (currentPlayer) {
            this.isHost = currentPlayer.isHost;
            this.isReady = currentPlayer.ready;
            this.currentTeam = currentPlayer.team;
        }

        this.updateUI();
    }

    // 更新UI
    updateUI() {
        // 更新玩家列表
        this.renderPlayersList();

        // 更新队伍选择
        this.renderTeamsList();

        // 更新游戏设置
        this.updateGameSettingsUI();

        // 更新准备状态
        this.updateReadyStatus();

        // 更新开始游戏按钮
        this.updateStartGameButton();

        // 更新状态消息
        this.updateStatusMessage();
    }

    // 渲染玩家列表
    renderPlayersList() {
        const playersHTML = this.players.map(player => `
            <div class="player-item">
                <div class="player-info">
                    <div class="player-name">
                        ${player.nickname}
                        ${player.isHost ? ' (房主)' : ''}
                        ${player.isSpectator ? ' (观众)' : ''}
                    </div>
                    <div class="player-status ${player.ready ? 'player-ready' : ''}">
                        ${player.isSpectator ? '观战中' : (player.ready ? '已准备' : '未准备')}
                    </div>
                </div>
                <div class="player-team">
                    ${player.isSpectator ? '' : `队伍 ${player.team}`}
                </div>
            </div>
        `).join('');

        this.elements.playersList.innerHTML = playersHTML;
    }

    // 渲染队伍选择
    renderTeamsList() {
        const maxTeams = Math.min(8, this.gameSettings.maxPlayers);
        const teamsHTML = [];

        for (let i = 1; i <= maxTeams; i++) {
            const playersInTeam = this.players.filter(p => p.team === i && !p.isSpectator).length;
            const isSelected = this.currentTeam === i;
            
            teamsHTML.push(`
                <div class="team-btn ${isSelected ? 'selected' : ''}" 
                     onclick="roomManager.changeTeam(${i})">
                    队伍 ${i}
                    <br>
                    <small>(${playersInTeam} 人)</small>
                </div>
            `);
        }

        this.elements.teamsList.innerHTML = teamsHTML.join('');
    }

    // 更新游戏设置UI
    updateGameSettingsUI() {
        if (this.isHost) {
            this.elements.gameMode.disabled = false;
            this.elements.gameSpeed.disabled = false;
            this.elements.maxPlayers.disabled = false;
        } else {
            this.elements.gameMode.disabled = true;
            this.elements.gameSpeed.disabled = true;
            this.elements.maxPlayers.disabled = true;
        }

        this.elements.gameMode.value = this.gameSettings.gameMode;
        this.elements.gameSpeed.value = this.gameSettings.gameSpeed;
        this.elements.maxPlayers.value = this.gameSettings.maxPlayers;
    }

    // 更新准备状态
    updateReadyStatus() {
        if (this.isReady) {
            this.elements.readyBtn.textContent = '取消准备';
            this.elements.readyBtn.className = 'btn btn-secondary';
            this.elements.readyStatus.textContent = '已准备';
            this.elements.readyStatus.className = 'ready-status ready';
        } else {
            this.elements.readyBtn.textContent = '准备';
            this.elements.readyBtn.className = 'btn btn-success';
            this.elements.readyStatus.textContent = '未准备';
            this.elements.readyStatus.className = 'ready-status';
        }
    }

    // 更新开始游戏按钮
    updateStartGameButton() {
        if (this.isHost) {
            const activePlayers = this.players.filter(p => !p.isSpectator);
            const readyPlayers = activePlayers.filter(p => p.ready);
            
            const canStart = activePlayers.length >= 2 && readyPlayers.length === activePlayers.length;
            
            this.elements.startGameBtn.disabled = !canStart;
            this.elements.startGameBtn.style.display = 'block';
        } else {
            this.elements.startGameBtn.style.display = 'none';
        }
    }

    // 更新状态消息
    updateStatusMessage() {
        const activePlayers = this.players.filter(p => !p.isSpectator);
        const readyPlayers = activePlayers.filter(p => p.ready);
        
        if (activePlayers.length < 2) {
            Utils.showStatus('等待更多玩家加入...');
        } else if (readyPlayers.length < activePlayers.length) {
            Utils.showStatus(`等待玩家准备 (${readyPlayers.length}/${activePlayers.length})`);
        } else {
            Utils.showStatus('所有玩家已准备，可以开始游戏！');
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待WebSocket连接建立
    const initRoom = () => {
        if (wsManager.getConnectionStatus()) {
            window.roomManager = new RoomManager();
        } else {
            setTimeout(initRoom, 500);
        }
    };

    setTimeout(initRoom, 100);
});
