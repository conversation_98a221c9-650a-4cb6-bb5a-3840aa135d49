#Background {
    background-color: rgb(56, 56, 56);
}

#Obstacle {
    border-image: url(":/img/Obstacle.png");
}

#Mountain {
    border-image: url(":/img/Mountain.png");
}

#City {
    border-image: url(":/img/City.png");
}

#General {
    border-image: url(":/img/General.png");
}

#Swamp {
    border-image: url(":/img/Swamp.png");
}

#Land {
    qproperty-focusPolicy: 0;
}

#Rank {
    background-color: rgba(255, 255, 255, 96);
    color: #e1f5fe;
}

#Shadow {
    background-color: rgba(0, 0, 0, 127);
}

#Focus {
    border-image: url(":/img/Focus.png");
}

.QLineEdit, .QTextEdit {
    background-color: rgba(0, 0, 0, 127);
    border-style: dashed;
    color: #ffffff;
}

.QTextEdit {
    qproperty-readOnly: 1;
}

QLabel {
    qproperty-focusPolicy: 0;
    qproperty-alignment: AlignCenter;
    color: #ffffff;
    background-color: transparent;
}

GameButton {
    /*qproperty-flat: 1;*/
    background-color: transparent;
    qproperty-focusPolicy: 0;
}
