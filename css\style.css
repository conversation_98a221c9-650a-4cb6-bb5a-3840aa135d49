/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@font-face {
    font-family: 'Quicksand Regular';
    src: url('../assets/font/Quicksand-Regular.ttf') format('truetype');
}

@font-face {
    font-family: 'Quicksand Medium';
    src: url('../assets/font/Quicksand-Medium.ttf') format('truetype');
}

@font-face {
    font-family: 'Quicksand Bold';
    src: url('../assets/font/Quicksand-Bold.ttf') format('truetype');
}

body {
    font-family: 'Quicksand Regular', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #81d4fa;
    min-height: 100vh;
    overflow-x: hidden;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 通用组件样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.btn {
    background-color: rgba(68, 138, 255, 0.8);
    color: rgba(255, 255, 255, 1);
    font-family: 'Quicksand Regular', sans-serif;
    font-size: 18px;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-height: 45px;
}

.btn:hover {
    background-color: rgba(68, 138, 255, 0.85);
}

.btn:active {
    background-color: rgba(68, 138, 255, 0.75);
}

.btn:disabled {
    background-color: rgba(68, 138, 255, 0.5);
    color: rgba(255, 255, 255, 0.5);
    cursor: not-allowed;
}

.input {
    background-color: rgba(0, 0, 0, 0.2);
    color: #b5b6e3;
    border: 0 solid #3574f0;
    border-bottom-width: 1px;
    font-family: 'Quicksand Medium', sans-serif;
    font-size: 18px;
    padding: 12px 16px;
    border-radius: 4px 4px 0 0;
    outline: none;
    transition: all 0.3s ease;
}

.input:focus {
    border-bottom-color: #81d4fa;
    background-color: rgba(0, 0, 0, 0.3);
}

.input::placeholder {
    color: rgba(181, 182, 227, 0.6);
}

.label {
    color: #81d4fa;
    font-family: 'Quicksand Bold', sans-serif;
    font-size: 18px;
    margin-bottom: 8px;
    display: block;
    text-align: center;
}

.radio-group {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.radio-item {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #81d4fa;
    font-size: 16px;
}

.radio-item input[type="radio"] {
    appearance: none;
    width: 16px;
    height: 16px;
    background-image: url('../assets/img/RadioButton.png');
    background-size: contain;
    background-repeat: no-repeat;
    cursor: pointer;
}

.radio-item input[type="radio"]:checked {
    background-image: url('../assets/img/RadioButton-Checked.png');
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: #81d4fa;
    font-size: 16px;
}

.checkbox-item input[type="checkbox"] {
    appearance: none;
    width: 16px;
    height: 16px;
    background-image: url('../assets/img/CheckBox.png');
    background-size: contain;
    background-repeat: no-repeat;
    cursor: pointer;
}

.checkbox-item input[type="checkbox"]:checked {
    background-image: url('../assets/img/CheckBox-Checked.png');
}

.disabled {
    opacity: 0.5;
    pointer-events: none;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .btn {
        font-size: 16px;
        padding: 10px 20px;
        min-height: 40px;
    }
    
    .input {
        font-size: 16px;
        padding: 10px 14px;
    }
    
    .label {
        font-size: 16px;
    }
    
    .radio-group {
        gap: 15px;
    }
}
