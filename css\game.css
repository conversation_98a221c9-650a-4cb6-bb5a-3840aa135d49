/* 游戏页面样式 */
.game-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

.game-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

.game-header {
    background: rgba(0, 0, 0, 0.8);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
}

.game-info {
    display: flex;
    gap: 20px;
    align-items: center;
}

.round-info {
    font-family: 'Quicksand Medium', sans-serif;
    font-size: 16px;
    color: #81d4fa;
}

.game-controls {
    display: flex;
    gap: 10px;
}

.game-controls .btn {
    padding: 6px 12px;
    font-size: 14px;
    min-height: 32px;
}

.game-board {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: #2c3e50;
}

.map-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    gap: 1px;
    background: #34495e;
    padding: 2px;
    border-radius: 4px;
}

.map-cell {
    width: 30px;
    height: 30px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Quicksand Medium', sans-serif;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.1s ease;
    border: 1px solid transparent;
}

.map-cell.land { background-color: #dcdcdc; color: #333; }
.map-cell.mountain { background-color: #666; color: #fff; }
.map-cell.city { background-color: #8b4513; color: #fff; }
.map-cell.general { background-color: #000; color: #fff; }
.map-cell.swamp { background-color: #4a5d23; color: #fff; }

.map-cell.player-1 { background-color: #f07178; }
.map-cell.player-2 { background-color: #82aaff; }
.map-cell.player-3 { background-color: #c3e88d; }
.map-cell.player-4 { background-color: #008080; }
.map-cell.player-5 { background-color: #ffcb6b; }
.map-cell.player-6 { background-color: #c792ea; }
.map-cell.player-7 { background-color: #f78c6c; }
.map-cell.player-8 { background-color: #ff9800; }

.map-cell.visible {
    opacity: 1;
}

.map-cell.fog {
    opacity: 0.3;
    background-color: #555 !important;
    color: #888 !important;
}

.map-cell.selected {
    border-color: #fff;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

.map-cell.path {
    border-color: #ffeb3b;
    box-shadow: 0 0 3px rgba(255, 235, 59, 0.6);
}

.map-cell::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    background-size: contain;
    background-repeat: no-repeat;
}

.map-cell.city::after {
    background-image: url('../assets/img/City.png');
}

.map-cell.general::after {
    background-image: url('../assets/img/General.png');
}

.map-cell.mountain::after {
    background-image: url('../assets/img/Mountain.png');
}

.game-sidebar {
    width: 250px;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(10px);
}

.leaderboard {
    flex: 1;
    padding: 20px;
}

.leaderboard-title {
    font-family: 'Quicksand Bold', sans-serif;
    font-size: 16px;
    color: #81d4fa;
    margin-bottom: 15px;
    text-align: center;
}

.player-stats {
    margin-bottom: 10px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.player-stats.current-player {
    background: rgba(68, 138, 255, 0.3);
}

.player-stats.eliminated {
    opacity: 0.5;
}

.player-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 8px;
}

.player-name {
    flex: 1;
    font-size: 12px;
    color: #81d4fa;
}

.player-army {
    font-size: 11px;
    color: rgba(129, 212, 250, 0.8);
}

.player-land {
    font-size: 11px;
    color: rgba(129, 212, 250, 0.6);
}

.chat-container {
    height: 200px;
    border-top: 1px solid rgba(129, 212, 250, 0.2);
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    padding: 10px;
    overflow-y: auto;
    font-size: 12px;
    line-height: 1.4;
}

.chat-message {
    margin-bottom: 5px;
    color: rgba(129, 212, 250, 0.8);
}

.chat-message .sender {
    font-weight: bold;
    color: #81d4fa;
}

.chat-input-container {
    padding: 10px;
    border-top: 1px solid rgba(129, 212, 250, 0.1);
}

.chat-input {
    width: 100%;
    padding: 6px 10px;
    font-size: 12px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(129, 212, 250, 0.3);
    border-radius: 4px;
    color: #b5b6e3;
    outline: none;
}

.chat-input:focus {
    border-color: #81d4fa;
}

/* 游戏结束覆盖层 */
.game-over-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.game-over-modal {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(129, 212, 250, 0.3);
}

.game-over-title {
    font-family: 'Quicksand Bold', sans-serif;
    font-size: 32px;
    color: #81d4fa;
    margin-bottom: 20px;
}

.game-over-result {
    font-size: 18px;
    color: rgba(129, 212, 250, 0.8);
    margin-bottom: 30px;
}

.game-over-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-container {
        flex-direction: column;
    }
    
    .game-sidebar {
        width: 100%;
        height: 200px;
        flex-direction: row;
    }
    
    .leaderboard {
        flex: 1;
        padding: 10px;
    }
    
    .chat-container {
        width: 200px;
        height: auto;
        border-top: none;
        border-left: 1px solid rgba(129, 212, 250, 0.2);
    }
    
    .map-cell {
        width: 25px;
        height: 25px;
        font-size: 10px;
    }
}
