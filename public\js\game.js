// 游戏主管理器
class GameManager {
    constructor() {
        this.roomId = Utils.getPathParameter();
        this.nickname = Utils.storage.get('nickname', '匿名玩家');
        
        // 游戏组件
        this.gameEngine = new GameEngine();
        this.gameRenderer = null;
        this.inputHandler = null;
        
        // UI元素
        this.elements = {};
        
        // 游戏状态
        this.isGameActive = false;
        this.players = [];
        this.leaderboard = [];
        
        this.initializeElements();
        this.setupWebSocketHandlers();
        this.initializeGame();
    }

    // 初始化DOM元素
    initializeElements() {
        this.elements = {
            canvas: document.getElementById('gameCanvas'),
            currentRound: document.getElementById('currentRound'),
            gameSpeed: document.getElementById('gameSpeed'),
            surrenderBtn: document.getElementById('surrenderBtn'),
            leaveGameBtn: document.getElementById('leaveGameBtn'),
            leaderboardList: document.getElementById('leaderboardList'),
            gameChatMessages: document.getElementById('gameChatMessages'),
            gameChatInput: document.getElementById('gameChatInput'),
            sendGameChatBtn: document.getElementById('sendGameChatBtn'),
            halfTroopsCheckbox: document.getElementById('halfTroopsCheckbox'),
            clearPathBtn: document.getElementById('clearPathBtn'),
            focusGeneralBtn: document.getElementById('focusGeneralBtn'),
            gameStatusMessage: document.getElementById('gameStatusMessage'),
            gameEndModal: document.getElementById('gameEndModal'),
            gameResult: document.getElementById('gameResult'),
            backToLobbyBtn: document.getElementById('backToLobbyBtn'),
            playAgainBtn: document.getElementById('playAgainBtn')
        };
    }

    // 设置WebSocket消息处理器
    setupWebSocketHandlers() {
        // 游戏开始
        wsManager.on('GameStart', (data) => {
            this.handleGameStart(data);
        });

        // 地图更新
        wsManager.on('MapUpdate', (data) => {
            this.handleMapUpdate(data);
        });

        // 游戏结束
        wsManager.on('GameEnd', (data) => {
            this.handleGameEnd(data);
        });

        // 聊天消息
        wsManager.on('Chat', (data) => {
            this.addChatMessage(data.nickname, data.message);
        });

        // 玩家离开
        wsManager.on('PlayerLeft', (data) => {
            this.addChatMessage('系统', `${data.nickname} 离开了游戏`);
        });
    }

    // 初始化游戏
    initializeGame() {
        // 初始化渲染器
        this.gameRenderer = new GameRenderer(this.elements.canvas, this.gameEngine);
        
        // 初始化输入处理器
        this.inputHandler = new GameInputHandler(
            this.elements.canvas, 
            this.gameRenderer, 
            this.gameEngine
        );
        
        // 设置事件监听器
        this.setupEventListeners();
        
        // 开始渲染循环
        this.startRenderLoop();
        
        // 加入游戏房间
        this.joinGameRoom();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 投降按钮
        this.elements.surrenderBtn.addEventListener('click', () => {
            this.surrender();
        });

        // 离开游戏按钮
        this.elements.leaveGameBtn.addEventListener('click', () => {
            this.leaveGame();
        });

        // 聊天功能
        this.elements.sendGameChatBtn.addEventListener('click', () => {
            this.sendChat();
        });

        this.elements.gameChatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChat();
            }
        });

        // 清除路径按钮
        this.elements.clearPathBtn.addEventListener('click', () => {
            this.inputHandler.clearPath();
        });

        // 定位将军按钮
        this.elements.focusGeneralBtn.addEventListener('click', () => {
            this.inputHandler.focusOnGeneral();
        });

        // 游戏结束模态框按钮
        this.elements.backToLobbyBtn.addEventListener('click', () => {
            Utils.navigateTo('/lobby');
        });

        this.elements.playAgainBtn.addEventListener('click', () => {
            Utils.navigateTo(`/room/${this.roomId}`);
        });
    }

    // 开始渲染循环
    startRenderLoop() {
        const render = () => {
            this.gameRenderer.render();
            this.updateUI();
            requestAnimationFrame(render);
        };
        render();
    }

    // 加入游戏房间
    joinGameRoom() {
        if (!wsManager.getConnectionStatus()) {
            Utils.showError('未连接到服务器');
            Utils.navigateTo('/大厅');
            return;
        }

        // 这里可以发送加入游戏的消息
        // 或者等待服务器发送游戏状态
    }

    // 处理游戏开始
    handleGameStart(data) {
        console.log('[Game] 游戏开始', data);
        
        this.isGameActive = true;
        this.gameEngine.initGame(data);
        
        // 设置玩家信息（需要从服务器获取）
        // this.gameEngine.setPlayerInfo(playerId, playerTeam);
        
        Utils.showSuccess('游戏开始！');
    }

    // 处理地图更新
    handleMapUpdate(data) {
        console.log('[Game] 地图更新', data);
        
        this.gameEngine.updateMap(data);
        this.updateLeaderboard();
        
        // 检查游戏是否结束
        if (this.gameEngine.checkGameEnd()) {
            this.handleGameEnd({ winner: this.gameEngine.winner });
        }
    }

    // 处理游戏结束
    handleGameEnd(data) {
        console.log('[Game] 游戏结束', data);
        
        this.isGameActive = false;
        this.showGameEndModal(data);
    }

    // 显示游戏结束模态框
    showGameEndModal(data) {
        const { winner } = data;
        
        let resultText = '';
        if (winner) {
            const gameState = this.gameEngine.getGameState();
            if (winner === gameState.playerId) {
                resultText = '🎉 恭喜你获得胜利！';
            } else {
                resultText = `游戏结束，玩家 ${winner} 获得胜利`;
            }
        } else {
            resultText = '游戏结束，平局';
        }
        
        this.elements.gameResult.innerHTML = `
            <h3>${resultText}</h3>
            <div class="final-leaderboard">
                ${this.renderFinalLeaderboard()}
            </div>
        `;
        
        this.elements.gameEndModal.style.display = 'flex';
    }

    // 渲染最终排行榜
    renderFinalLeaderboard() {
        const leaderboard = this.gameEngine.getLeaderboard();
        
        return leaderboard.map(player => `
            <div class="leaderboard-item">
                <span class="rank">#${player.rank}</span>
                <span class="player">玩家 ${player.playerId}</span>
                <span class="stats">
                    军队: ${Utils.formatNumber(player.army)} | 
                    领土: ${player.land}
                </span>
            </div>
        `).join('');
    }

    // 更新UI
    updateUI() {
        const gameState = this.gameEngine.getGameState();
        
        // 更新回合数
        if (this.elements.currentRound) {
            this.elements.currentRound.textContent = gameState.currentRound;
        }
        
        // 更新游戏速度
        if (this.elements.gameSpeed) {
            this.elements.gameSpeed.textContent = gameState.gameSpeed + 'x';
        }
    }

    // 更新排行榜
    updateLeaderboard() {
        const leaderboard = this.gameEngine.getLeaderboard();
        
        const leaderboardHTML = leaderboard.map(player => `
            <div class="leaderboard-item">
                <div class="player-rank">${player.rank}</div>
                <div class="player-name">玩家 ${player.playerId}</div>
                <div class="player-stats">
                    <div class="stat-army">
                        <div class="stat-value">${Utils.formatNumber(player.army)}</div>
                        <div class="stat-label">军队</div>
                    </div>
                    <div class="stat-land">
                        <div class="stat-value">${player.land}</div>
                        <div class="stat-label">领土</div>
                    </div>
                </div>
            </div>
        `).join('');
        
        this.elements.leaderboardList.innerHTML = leaderboardHTML;
    }

    // 发送移动命令
    sendMove(path, halfTroops = false) {
        if (!this.isGameActive || !path || path.length < 2) return;
        
        wsManager.send('Move', {
            path: path,
            halfTroops: halfTroops
        });
        
        console.log('[Game] 发送移动命令', { path, halfTroops });
    }

    // 发送聊天消息
    sendChat() {
        const message = this.elements.gameChatInput.value.trim();
        if (!message) return;
        
        wsManager.send('Chat', { message });
        this.elements.gameChatInput.value = '';
    }

    // 添加聊天消息
    addChatMessage(nickname, message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message';
        messageElement.innerHTML = `
            <span class="chat-nickname">${nickname}:</span>
            <span class="chat-text">${message}</span>
        `;
        
        this.elements.gameChatMessages.appendChild(messageElement);
        this.elements.gameChatMessages.scrollTop = this.elements.gameChatMessages.scrollHeight;
    }

    // 投降
    surrender() {
        if (!this.isGameActive) return;
        
        if (confirm('确定要投降吗？')) {
            wsManager.send('Surrender');
            Utils.showStatus('已投降');
        }
    }

    // 离开游戏
    leaveGame() {
        if (confirm('确定要离开游戏吗？')) {
            Utils.navigateTo('/大厅');
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待WebSocket连接建立
    const initGame = () => {
        if (wsManager.getConnectionStatus()) {
            window.gameManager = new GameManager();
        } else {
            setTimeout(initGame, 500);
        }
    };
    
    setTimeout(initGame, 100);
});
