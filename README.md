# Generals.io 网页版

这是基于原版 Generals.OI 项目的网页版本，保持了所有原有功能，并按照用户要求进行了调整。

## 主要特性

- 🌐 **纯网页版本**：无需安装，直接在浏览器中运行
- 🏠 **大厅系统**：昵称设置、创建房间、房间列表、通过房间号加入
- 🎮 **完整游戏体验**：保持原版所有游戏机制和UI风格
- 📱 **响应式设计**：支持桌面和移动设备
- 🎨 **原版UI风格**：使用原版颜色、字体和图标

## 路由结构

- `/大厅` - 大厅页面（昵称输入、创建/加入房间）
- `/room/房间号` - 房间设置页面（4位数字房间号）
- `/game/房间号` - 游戏页面

## 文件结构

```
├── index.html          # 主页面
├── css/               # 样式文件
│   ├── style.css      # 基础样式
│   ├── lobby.css      # 大厅样式
│   ├── room.css       # 房间样式
│   └── game.css       # 游戏样式
├── js/                # JavaScript文件
│   ├── router.js      # 路由管理
│   ├── websocket.js   # WebSocket通信
│   ├── game.js        # 游戏核心逻辑
│   └── app.js         # 应用主入口
├── assets/            # 资源文件
│   ├── img/          # 图片资源
│   └── font/         # 字体文件
├── server.py          # 简单HTTP服务器（用于测试）
└── README.md          # 说明文档
```

## 快速开始

### 方法1：使用Python服务器（推荐）

1. 确保已安装Python 3
2. 在项目根目录运行：
   ```bash
   python server.py
   ```
3. 打开浏览器访问：http://localhost:8000/大厅

### 方法2：使用其他HTTP服务器

你也可以使用任何HTTP服务器来托管这些文件，例如：

- **Node.js**: `npx http-server`
- **PHP**: `php -S localhost:8000`
- **Apache/Nginx**: 配置虚拟主机

## 游戏功能

### 大厅功能
- ✅ 昵称设置（最多15个字符）
- ✅ 一键创建房间（自动生成4位房间号）
- ✅ 通过房间号加入房间
- ✅ 房间列表显示
- ✅ 刷新房间列表

### 房间功能
- ✅ 游戏设置调整
  - 视野类型：近视/迷雾/透明
  - 游戏速度：1x到10x
  - 修改器：跳跃、城邦
  - 组队：启用/禁用
- ✅ 队伍选择（8个队伍颜色）
- ✅ 玩家列表和准备状态
- ✅ 开始游戏

### 游戏功能
- ✅ 地图渲染和显示
- ✅ 鼠标点击移动
- ✅ 键盘控制
  - WASD/方向键：移动焦点
  - Q：清除选择
  - E：取消移动
  - Z：切换半移动
  - G：聚焦将军
  - Enter：聊天输入
- ✅ 实时聊天系统
- ✅ 排行榜显示
- ✅ 游戏结束处理

## 键盘控制

| 按键 | 功能 |
|------|------|
| W/A/S/D 或 方向键 | 移动光标/执行移动 |
| Q | 清除当前选择 |
| E | 取消最后一步移动 |
| Z | 切换半移动模式 |
| G | 聚焦到将军位置 |
| Enter | 激活聊天输入 |

## 技术特性

- **纯前端实现**：使用原生JavaScript，无需框架
- **模块化设计**：路由、WebSocket、游戏逻辑分离
- **WebSocket通信**：与原版服务器协议兼容
- **响应式布局**：适配不同屏幕尺寸
- **原版资源**：使用原版图片、字体和颜色

## 与原版的差异

1. **加入房间方式**：改为输入4位数字房间号
2. **运行平台**：从Qt桌面应用改为网页应用
3. **部署方式**：可以部署到任何Web服务器

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 开发说明

如果需要修改或扩展功能：

1. **样式修改**：编辑 `css/` 目录下的文件
2. **功能扩展**：修改 `js/` 目录下的对应文件
3. **资源更新**：替换 `assets/` 目录下的文件

## 注意事项

- 需要配合原版服务器使用（WebSocket端口32767）
- 房间号为4位数字（1000-9999）
- 保存的昵称存储在浏览器本地存储中
- 建议使用现代浏览器以获得最佳体验

## 许可证

本项目基于原版 Generals.OI 项目，遵循相同的许可证条款。
