// 大厅页面逻辑
class LobbyManager {
    constructor() {
        this.nickname = '';
        this.roomList = [];
        this.isLoading = false;
        
        this.initializeElements();
        this.setupEventListeners();
        this.setupWebSocketHandlers();
        this.loadSavedNickname();
        this.refreshRoomList();
    }

    // 初始化DOM元素
    initializeElements() {
        this.elements = {
            nickname: document.getElementById('nickname'),
            createRoomBtn: document.getElementById('createRoomBtn'),
            joinRoomBtn: document.getElementById('joinRoomBtn'),
            roomIdInput: document.getElementById('roomIdInput'),
            refreshRoomsBtn: document.getElementById('refreshRoomsBtn'),
            roomList: document.getElementById('roomList'),
            statusMessage: document.getElementById('statusMessage')
        };
    }

    // 设置事件监听器
    setupEventListeners() {
        // 昵称输入
        this.elements.nickname.addEventListener('input', (e) => {
            this.nickname = e.target.value.trim();
            this.saveNickname();
        });

        // 创建房间按钮
        this.elements.createRoomBtn.addEventListener('click', () => {
            this.createRoom();
        });

        // 加入房间按钮
        this.elements.joinRoomBtn.addEventListener('click', () => {
            this.joinRoom();
        });

        // 房间号输入框回车
        this.elements.roomIdInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.joinRoom();
            }
        });

        // 刷新房间列表按钮
        this.elements.refreshRoomsBtn.addEventListener('click', () => {
            this.refreshRoomList();
        });

        // 房间号输入限制
        this.elements.roomIdInput.addEventListener('input', (e) => {
            // 只允许数字
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
            // 限制4位
            if (e.target.value.length > 4) {
                e.target.value = e.target.value.slice(0, 4);
            }
        });
    }

    // 设置WebSocket消息处理器
    setupWebSocketHandlers() {
        // 房间创建成功
        wsManager.on('RoomCreated', (data) => {
            const { roomId } = data;
            Utils.showSuccess(`房间 ${roomId} 创建成功！`);

            // 显示跳转提示
            const statusElement = document.getElementById('statusMessage');
            if (statusElement) {
                statusElement.innerHTML = `
                    <div class="success-message">
                        <p>房间 ${roomId} 创建成功！</p>
                        <button onclick="window.location.href='/room/${roomId}'" class="btn btn-primary">
                            进入房间
                        </button>
                    </div>
                `;
            }
        });

        // 加入房间结果
        wsManager.on('JoinResult', (data) => {
            if (data.success) {
                const roomId = this.elements.roomIdInput.value;
                Utils.showSuccess('成功加入房间！');
                Utils.navigateTo(`/room/${roomId}`);
            } else {
                Utils.showError(data.error || '加入房间失败');
            }
        });

        // 房间列表
        wsManager.on('RoomList', (data) => {
            this.roomList = data.rooms || [];
            this.renderRoomList();
        });
    }

    // 加载保存的昵称
    loadSavedNickname() {
        const savedNickname = Utils.storage.get('nickname', '匿名玩家');
        this.elements.nickname.value = savedNickname;
        this.nickname = savedNickname;
    }

    // 保存昵称
    saveNickname() {
        Utils.storage.set('nickname', this.nickname);
    }

    // 创建房间
    createRoom() {
        if (!this.validateNickname()) {
            return;
        }

        if (!wsManager.getConnectionStatus()) {
            Utils.showError('未连接到服务器');
            return;
        }

        this.elements.createRoomBtn.disabled = true;
        this.elements.createRoomBtn.textContent = '创建中...';

        wsManager.send('CreateRoom', {
            nickname: this.nickname
        });

        // 5秒后恢复按钮状态
        setTimeout(() => {
            this.elements.createRoomBtn.disabled = false;
            this.elements.createRoomBtn.textContent = '创建房间';
        }, 5000);
    }

    // 加入房间
    joinRoom() {
        if (!this.validateNickname()) {
            return;
        }

        const roomId = this.elements.roomIdInput.value.trim();
        const roomValidation = Utils.validateRoomId(roomId);
        
        if (!roomValidation.valid) {
            Utils.showError(roomValidation.error);
            return;
        }

        if (!wsManager.getConnectionStatus()) {
            Utils.showError('未连接到服务器');
            return;
        }

        this.elements.joinRoomBtn.disabled = true;
        this.elements.joinRoomBtn.textContent = '加入中...';

        wsManager.send('JoinRoom', {
            roomId: roomId,
            nickname: this.nickname
        });

        // 5秒后恢复按钮状态
        setTimeout(() => {
            this.elements.joinRoomBtn.disabled = false;
            this.elements.joinRoomBtn.textContent = '加入房间';
        }, 5000);
    }

    // 验证昵称
    validateNickname() {
        const validation = Utils.validateNickname(this.nickname);
        if (!validation.valid) {
            Utils.showError(validation.error);
            this.elements.nickname.focus();
            return false;
        }
        return true;
    }

    // 刷新房间列表
    refreshRoomList() {
        if (this.isLoading) {
            return;
        }

        if (!wsManager.getConnectionStatus()) {
            Utils.showError('未连接到服务器');
            return;
        }

        this.isLoading = true;
        this.elements.refreshRoomsBtn.disabled = true;
        this.elements.refreshRoomsBtn.textContent = '刷新中...';

        // 显示加载状态
        this.elements.roomList.innerHTML = '<div class="loading">正在加载房间列表...</div>';

        wsManager.send('GetRoomList');

        // 3秒后恢复按钮状态
        setTimeout(() => {
            this.isLoading = false;
            this.elements.refreshRoomsBtn.disabled = false;
            this.elements.refreshRoomsBtn.textContent = '刷新';
        }, 3000);
    }

    // 渲染房间列表
    renderRoomList() {
        if (this.roomList.length === 0) {
            this.elements.roomList.innerHTML = `
                <div class="loading">
                    暂无可用房间<br>
                    <small>创建一个新房间开始游戏吧！</small>
                </div>
            `;
            return;
        }

        const roomListHTML = this.roomList.map(room => `
            <div class="room-item" onclick="lobbyManager.joinRoomById('${room.id}')">
                <div class="room-info">
                    <div class="room-id">房间 ${room.id}</div>
                    <div class="room-details">
                        ${room.playerCount}/${room.maxPlayers} 玩家 | 
                        ${this.getGameModeName(room.gameMode)}
                    </div>
                </div>
                <div class="room-status">
                    ${room.playerCount < room.maxPlayers ? '可加入' : '已满'}
                </div>
            </div>
        `).join('');

        this.elements.roomList.innerHTML = roomListHTML;
    }

    // 通过房间ID加入房间
    joinRoomById(roomId) {
        if (!this.validateNickname()) {
            return;
        }

        this.elements.roomIdInput.value = roomId;
        this.joinRoom();
    }

    // 获取游戏模式名称
    getGameModeName(gameMode) {
        const modes = {
            0: '经典模式',
            1: '城邦模式',
            2: '迷雾模式'
        };
        return modes[gameMode] || '未知模式';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 等待WebSocket连接建立
    const initLobby = () => {
        if (wsManager.getConnectionStatus()) {
            window.lobbyManager = new LobbyManager();
        } else {
            // 如果WebSocket未连接，等待一段时间后重试
            setTimeout(initLobby, 500);
        }
    };

    // 延迟初始化，确保WebSocket连接已建立
    setTimeout(initLobby, 100);
});
