// 应用程序主入口
class App {
    constructor() {
        this.init();
    }

    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.onDOMReady();
            });
        } else {
            this.onDOMReady();
        }
    }

    onDOMReady() {
        console.log('App initialized');
        
        // 设置全局错误处理
        this.setupErrorHandling();
        
        // 设置WebSocket消息处理器
        this.setupWebSocketHandlers();
        
        // 初始化路由
        if (window.router) {
            console.log('Router initialized');
        }
        
        // 设置连接状态监听
        this.setupConnectionStatusListener();
        
        // 尝试自动连接到本地服务器
        this.attemptAutoConnect();
    }

    // 设置错误处理
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
        });
    }

    // 设置WebSocket消息处理器
    setupWebSocketHandlers() {
        if (!window.wsManager) return;

        // 玩家信息处理
        window.wsManager.onMessage('PlayerInfo', (data) => {
            if (data.length >= 2) {
                const playerInfo = {
                    id: data[0],
                    team: data[1]
                };
                window.gameManager.setPlayerInfo(playerInfo);
                console.log('Player info set:', playerInfo);
            }
        });

        // 所有玩家信息处理
        window.wsManager.onMessage('PlayersInfo', (data) => {
            if (data.length >= 1) {
                const playerCount = data[0];
                const playersInfo = {};
                
                for (let i = 1; i <= playerCount; i++) {
                    if (data[i] && data[i].length >= 3) {
                        const playerData = data[i];
                        playersInfo[playerData[1]] = {
                            name: playerData[0],
                            id: playerData[1],
                            team: playerData[2]
                        };
                    }
                }
                
                window.gameManager.setPlayersInfo(playersInfo);
                console.log('Players info set:', playersInfo);
            }
        });

        // 房间状态更新
        window.wsManager.onMessage('RoomUpdate', (data) => {
            this.handleRoomUpdate(data);
        });

        // 游戏开始
        window.wsManager.onMessage('GameStart', (data) => {
            const currentPath = window.location.pathname;
            if (currentPath.startsWith('/room/')) {
                const roomId = currentPath.split('/')[2];
                window.router.navigate(`/game/${roomId}`);
            }
        });
    }

    // 设置连接状态监听
    setupConnectionStatusListener() {
        window.addEventListener('connectionStatusChanged', (event) => {
            const { isConnected } = event.detail;
            this.updateConnectionUI(isConnected);
        });
    }

    // 更新连接状态UI
    updateConnectionUI(isConnected) {
        // 更新连接状态指示器
        const statusElements = document.querySelectorAll('.connection-status');
        statusElements.forEach(element => {
            element.textContent = isConnected ? '已连接' : '未连接';
            element.className = `connection-status ${isConnected ? 'connected' : 'disconnected'}`;
        });

        // 根据连接状态启用/禁用相关按钮
        const connectButtons = document.querySelectorAll('.connect-required');
        connectButtons.forEach(button => {
            button.disabled = !isConnected;
        });
    }

    // 尝试自动连接
    attemptAutoConnect() {
        // 检查是否在房间或游戏页面
        const path = window.location.pathname;
        if (path.startsWith('/room/') || path.startsWith('/game/')) {
            // 自动连接到本地服务器
            setTimeout(() => {
                window.wsManager.connect('localhost');
            }, 1000);
        }
    }

    // 处理房间更新
    handleRoomUpdate(data) {
        const currentPath = window.location.pathname;
        
        if (currentPath.startsWith('/room/')) {
            // 更新房间页面
            this.updateRoomPage(data);
        }
    }

    // 更新房间页面
    updateRoomPage(roomData) {
        // 更新玩家列表
        if (roomData.players) {
            const playerList = document.getElementById('player-list');
            if (playerList) {
                const playersTitle = document.querySelector('.players-title');
                if (playersTitle) {
                    playersTitle.textContent = `玩家 (${roomData.players.length}/8)`;
                }

                playerList.innerHTML = roomData.players.map(player => `
                    <div class="player-item">
                        <div class="player-info">
                            <div class="player-name">${player.name}</div>
                            <div class="player-team">队伍 ${player.team}</div>
                        </div>
                        <div class="player-status ${player.ready ? 'ready' : 'not-ready'}">
                            ${player.ready ? '准备' : '未准备'}
                        </div>
                    </div>
                `).join('');
            }
        }

        // 更新开始游戏按钮状态
        const startGameBtn = document.getElementById('start-game-btn');
        if (startGameBtn && roomData.canStart !== undefined) {
            startGameBtn.disabled = !roomData.canStart;
        }

        // 更新房间状态
        const roomStatus = document.getElementById('room-status');
        if (roomStatus && roomData.status) {
            roomStatus.textContent = roomData.status;
        }
    }

    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#f44336' : type === 'success' ? '#4caf50' : '#2196f3'};
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            z-index: 10000;
            font-family: 'Quicksand Medium', sans-serif;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            animation: slideInRight 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 显示加载状态
    showLoading(message = '加载中...') {
        const loading = document.createElement('div');
        loading.id = 'loading-overlay';
        loading.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-message">${message}</div>
            </div>
        `;
        
        loading.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        `;
        
        const style = document.createElement('style');
        style.textContent = `
            .loading-content {
                text-align: center;
                color: #81d4fa;
                font-family: 'Quicksand Medium', sans-serif;
            }
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid rgba(129, 212, 250, 0.3);
                border-top: 4px solid #81d4fa;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            }
            .loading-message {
                font-size: 16px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(loading);
    }

    // 隐藏加载状态
    hideLoading() {
        const loading = document.getElementById('loading-overlay');
        if (loading) {
            loading.remove();
        }
    }

    // 确认对话框
    confirm(message, callback) {
        const overlay = document.createElement('div');
        overlay.className = 'confirm-overlay';
        overlay.innerHTML = `
            <div class="confirm-modal">
                <div class="confirm-message">${message}</div>
                <div class="confirm-actions">
                    <button class="btn confirm-cancel">取消</button>
                    <button class="btn confirm-ok">确定</button>
                </div>
            </div>
        `;
        
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        `;
        
        const style = document.createElement('style');
        style.textContent = `
            .confirm-modal {
                background: rgba(0, 0, 0, 0.9);
                border-radius: 12px;
                padding: 30px;
                text-align: center;
                backdrop-filter: blur(20px);
                border: 1px solid rgba(129, 212, 250, 0.3);
                min-width: 300px;
            }
            .confirm-message {
                color: #81d4fa;
                font-family: 'Quicksand Medium', sans-serif;
                font-size: 16px;
                margin-bottom: 20px;
            }
            .confirm-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(overlay);
        
        // 绑定事件
        overlay.querySelector('.confirm-cancel').addEventListener('click', () => {
            overlay.remove();
            if (callback) callback(false);
        });
        
        overlay.querySelector('.confirm-ok').addEventListener('click', () => {
            overlay.remove();
            if (callback) callback(true);
        });
        
        // 点击背景关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
                if (callback) callback(false);
            }
        });
    }
}

// 创建全局应用实例
window.app = new App();

// 全局工具函数
window.utils = {
    // 格式化时间
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // 生成随机ID
    generateId(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};
