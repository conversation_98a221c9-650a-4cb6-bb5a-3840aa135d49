<RCC>
    <qresource prefix="/">
        <file>qss/WindowWidgets.qss</file>
        <file>qss/GameWindowWidgets.qss</file>
        <file>font/Quicksand-Medium.ttf</file>
        <file>font/Quicksand-Bold.ttf</file>
        <file>font/Quicksand-Regular.ttf</file>
        <file>aud/GongSound.mp3</file>
        <file>aud/GongSound.wav</file>
        <file>img/Arrow-Down.png</file>
        <file>img/Arrow-Left.png</file>
        <file>img/Arrow-Right.png</file>
        <file>img/Arrow-Up.png</file>
        <file>img/City.png</file>
        <file>img/General.png</file>
        <file>img/General-Lost.png</file>
        <file>img/General-Won.png</file>
        <file>img/CloseButton.png</file>
        <file>img/CloseButton-Clicked.png</file>
        <file>img/CloseButton-Hover.png</file>
        <file>img/Focus.png</file>
        <file>img/Icon.ico</file>
        <file>img/Icon.png</file>
        <file>img/MinimizeButton.png</file>
        <file>img/MinimizeButton-Clicked.png</file>
        <file>img/MinimizeButton-Hover.png</file>
        <file>img/Mountain.png</file>
        <file>img/Obstacle.png</file>
        <file>img/Swamp.png</file>
        <file>img/General-Gray.png</file>
        <file>img/General-Blue.png</file>
        <file>img/Icon-Blue.ico</file>
        <file>img/Icon-Blue.png</file>
        <file>img/RadioButton.png</file>
        <file>img/RadioButton-Checked.png</file>
        <file>img/CheckBox.png</file>
        <file>img/CheckBox-Checked.png</file>
    </qresource>
</RCC>
