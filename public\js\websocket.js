// WebSocket 连接管理
class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.messageHandlers = new Map();
        this.onConnectionChange = null;
        this.heartbeatInterval = null;
    }

    // 连接到服务器
    connect() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}`;

            console.log('[WebSocket] 尝试连接到:', wsUrl);
            this.socket = new WebSocket(wsUrl);
            
            this.socket.onopen = () => {
                console.log('[WebSocket] 连接已建立');
                this.isConnected = true;
                this.reconnectAttempts = 0;

                if (this.onConnectionChange) {
                    this.onConnectionChange(true);
                }

                // 连接成功后立即请求房间列表
                setTimeout(() => {
                    this.send('GetRoomList');
                }, 100);
            };

            this.socket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleMessage(message);
                } catch (error) {
                    console.error('[WebSocket] 消息解析错误:', error);
                }
            };

            this.socket.onclose = (event) => {
                console.log('[WebSocket] 连接已关闭', event.code, event.reason);
                this.isConnected = false;
                this.stopHeartbeat();

                if (this.onConnectionChange) {
                    this.onConnectionChange(false);
                }

                // 尝试重连
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    console.log(`[WebSocket] 尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                    
                    setTimeout(() => {
                        this.connect();
                    }, this.reconnectDelay * this.reconnectAttempts);
                } else {
                    console.error('[WebSocket] 重连失败，已达到最大尝试次数');
                }
            };

            this.socket.onerror = (error) => {
                console.error('[WebSocket] 连接错误:', error);
            };

        } catch (error) {
            console.error('[WebSocket] 连接失败:', error);
        }
    }

    // 断开连接
    disconnect() {
        this.stopHeartbeat();
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        this.isConnected = false;
    }

    // 开始心跳
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected) {
                this.send('Ping');
            }
        }, 30000); // 每30秒发送一次心跳
    }

    // 停止心跳
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    // 发送消息
    send(type, data = {}) {
        if (!this.isConnected || !this.socket) {
            console.warn('[WebSocket] 连接未建立，无法发送消息');
            return false;
        }

        try {
            const message = {
                type: type,
                data: data
            };
            
            this.socket.send(JSON.stringify(message));
            console.log(`[WebSocket] 发送消息: ${type}`, data);
            return true;
        } catch (error) {
            console.error('[WebSocket] 发送消息失败:', error);
            return false;
        }
    }

    // 处理接收到的消息
    handleMessage(message) {
        const { type, data } = message;
        console.log(`[WebSocket] 收到消息: ${type}`, data);

        // 调用注册的处理器
        if (this.messageHandlers.has(type)) {
            const handlers = this.messageHandlers.get(type);
            handlers.forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`[WebSocket] 消息处理器错误 (${type}):`, error);
                }
            });
        } else {
            console.warn(`[WebSocket] 未找到消息处理器: ${type}`);
        }
    }

    // 注册消息处理器
    on(messageType, handler) {
        if (!this.messageHandlers.has(messageType)) {
            this.messageHandlers.set(messageType, []);
        }
        this.messageHandlers.get(messageType).push(handler);
    }

    // 移除消息处理器
    off(messageType, handler) {
        if (this.messageHandlers.has(messageType)) {
            const handlers = this.messageHandlers.get(messageType);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    // 获取连接状态
    getConnectionStatus() {
        return this.isConnected;
    }
}

// 全局WebSocket管理器实例
const wsManager = new WebSocketManager();

// 工具函数
const Utils = {
    // 显示状态消息
    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('statusMessage') || 
                            document.getElementById('gameStatusMessage');
        
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.className = `status-message status-${type}`;
        }
        
        console.log(`[Status] ${message}`);
    },

    // 显示错误消息
    showError(message) {
        this.showStatus(message, 'error');
        console.error(`[Error] ${message}`);
    },

    // 显示成功消息
    showSuccess(message) {
        this.showStatus(message, 'success');
        console.log(`[Success] ${message}`);
    },

    // 获取URL参数
    getUrlParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    // 获取路径参数（如房间号）
    getPathParameter() {
        const path = window.location.pathname;
        const segments = path.split('/').filter(segment => segment);
        return segments[segments.length - 1];
    },

    // 跳转页面
    navigateTo(url) {
        window.location.href = url;
    },

    // 格式化数字
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    },

    // 验证昵称
    validateNickname(nickname) {
        if (!nickname || nickname.trim().length === 0) {
            return { valid: false, error: '昵称不能为空' };
        }
        
        if (nickname.length > 15) {
            return { valid: false, error: '昵称不能超过15个字符' };
        }
        
        // 检查特殊字符
        const invalidChars = /[<>\"'&]/;
        if (invalidChars.test(nickname)) {
            return { valid: false, error: '昵称包含无效字符' };
        }
        
        return { valid: true };
    },

    // 验证房间号
    validateRoomId(roomId) {
        if (!roomId || roomId.length !== 4) {
            return { valid: false, error: '房间号必须是4位数字' };
        }
        
        if (!/^\d{4}$/.test(roomId)) {
            return { valid: false, error: '房间号只能包含数字' };
        }
        
        return { valid: true };
    },

    // 本地存储操作
    storage: {
        set(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (error) {
                console.warn('[Storage] 保存失败:', error);
            }
        },

        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.warn('[Storage] 读取失败:', error);
                return defaultValue;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(key);
            } catch (error) {
                console.warn('[Storage] 删除失败:', error);
            }
        }
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// 页面加载完成后自动连接WebSocket
document.addEventListener('DOMContentLoaded', () => {
    // 连接WebSocket
    wsManager.connect();
    
    // 监听连接状态变化
    wsManager.onConnectionChange = (connected) => {
        if (connected) {
            Utils.showSuccess('已连接到服务器');
        } else {
            Utils.showError('与服务器连接断开');
        }
    };
    
    // 页面卸载时断开连接
    window.addEventListener('beforeunload', () => {
        wsManager.disconnect();
    });
});
