const express = require('express');
const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const cors = require('cors');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 游戏状态管理
class GameServer {
    constructor() {
        this.rooms = new Map(); // 房间ID -> 房间对象
        this.clients = new Map(); // WebSocket -> 客户端信息
        this.roomTimers = new Map(); // 房间ID -> 游戏定时器
    }

    // 生成4位房间号
    generateRoomId() {
        let roomId;
        do {
            roomId = Math.floor(1000 + Math.random() * 9000).toString();
        } while (this.rooms.has(roomId));
        return roomId;
    }

    // 创建房间
    createRoom(hostSocket, nickname) {
        const roomId = this.generateRoomId();
        const room = {
            id: roomId,
            host: hostSocket,
            players: new Map(), // socket -> 玩家信息
            gameState: 'waiting', // waiting, playing, finished
            settings: {
                gameMode: 0,
                gameSpeed: 1.0,
                maxPlayers: 8
            },
            gameMap: null,
            gameRound: 0,
            startTime: null
        };

        // 添加房主
        room.players.set(hostSocket, {
            id: 1,
            nickname: nickname,
            team: 1,
            ready: false,
            isHost: true,
            isSpectator: false
        });

        this.rooms.set(roomId, room);
        this.clients.set(hostSocket, { roomId, nickname });
        
        console.log(`[Server] 房间 ${roomId} 已创建，房主: ${nickname}`);
        return roomId;
    }

    // 加入房间
    joinRoom(socket, roomId, nickname) {
        const room = this.rooms.get(roomId);
        if (!room) {
            return { success: false, error: '房间不存在' };
        }

        if (room.gameState === 'playing') {
            return { success: false, error: '游戏已开始' };
        }

        if (room.players.size >= room.settings.maxPlayers) {
            // 作为观众加入
            room.players.set(socket, {
                id: -1,
                nickname: `[观众] ${nickname}`,
                team: -1,
                ready: true,
                isHost: false,
                isSpectator: true
            });
        } else {
            // 作为玩家加入
            const playerId = room.players.size + 1;
            room.players.set(socket, {
                id: playerId,
                nickname: nickname,
                team: playerId,
                ready: false,
                isHost: false,
                isSpectator: false
            });
        }

        this.clients.set(socket, { roomId, nickname });
        console.log(`[Server] ${nickname} 加入房间 ${roomId}`);
        return { success: true };
    }

    // 获取房间列表
    getRoomList() {
        const roomList = [];
        for (const [roomId, room] of this.rooms) {
            if (room.gameState === 'waiting') {
                roomList.push({
                    id: roomId,
                    playerCount: Array.from(room.players.values()).filter(p => !p.isSpectator).length,
                    maxPlayers: room.settings.maxPlayers,
                    gameMode: room.settings.gameMode
                });
            }
        }
        return roomList;
    }

    // 广播消息到房间
    broadcastToRoom(roomId, message, excludeSocket = null) {
        const room = this.rooms.get(roomId);
        if (!room) return;

        for (const socket of room.players.keys()) {
            if (socket !== excludeSocket && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify(message));
            }
        }
    }

    // 处理客户端断开连接
    handleDisconnect(socket) {
        const clientInfo = this.clients.get(socket);
        if (!clientInfo) return;

        const room = this.rooms.get(clientInfo.roomId);
        if (room) {
            const player = room.players.get(socket);
            if (player) {
                console.log(`[Server] ${player.nickname} 离开房间 ${clientInfo.roomId}`);
                room.players.delete(socket);

                // 如果是房主离开，转移房主权限
                if (player.isHost && room.players.size > 0) {
                    const newHost = Array.from(room.players.keys())[0];
                    const newHostPlayer = room.players.get(newHost);
                    newHostPlayer.isHost = true;
                    room.host = newHost;
                }

                // 如果房间为空，删除房间
                if (room.players.size === 0) {
                    this.rooms.delete(clientInfo.roomId);
                    if (this.roomTimers.has(clientInfo.roomId)) {
                        clearInterval(this.roomTimers.get(clientInfo.roomId));
                        this.roomTimers.delete(clientInfo.roomId);
                    }
                    console.log(`[Server] 房间 ${clientInfo.roomId} 已删除`);
                } else {
                    // 通知其他玩家
                    this.broadcastToRoom(clientInfo.roomId, {
                        type: 'PlayerLeft',
                        data: { nickname: player.nickname }
                    });
                    this.updateRoomStatus(clientInfo.roomId);
                }
            }
        }

        this.clients.delete(socket);
    }

    // 更新房间状态
    updateRoomStatus(roomId) {
        const room = this.rooms.get(roomId);
        if (!room) return;

        const playersInfo = Array.from(room.players.values()).map(player => ({
            id: player.id,
            nickname: player.nickname,
            team: player.team,
            ready: player.ready,
            isHost: player.isHost,
            isSpectator: player.isSpectator
        }));

        this.broadcastToRoom(roomId, {
            type: 'RoomStatus',
            data: {
                players: playersInfo,
                gameState: room.gameState,
                settings: room.settings
            }
        });
    }
}

const gameServer = new GameServer();

// WebSocket 连接处理
wss.on('connection', (ws) => {
    console.log('[Server] 新的WebSocket连接');

    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            handleMessage(ws, message);
        } catch (error) {
            console.error('[Server] 消息解析错误:', error);
        }
    });

    ws.on('close', (code, reason) => {
        console.log(`[Server] WebSocket连接关闭: ${code} ${reason}`);
        gameServer.handleDisconnect(ws);
    });

    ws.on('error', (error) => {
        console.error('[Server] WebSocket错误:', error);
    });

    // 发送连接确认
    ws.send(JSON.stringify({
        type: 'Connected',
        data: { message: '连接成功' }
    }));
});

// 消息处理函数
function handleMessage(socket, message) {
    const { type, data } = message;
    console.log(`[Server] 收到消息: ${type}`);

    switch (type) {
        case 'CreateRoom':
            const roomId = gameServer.createRoom(socket, data.nickname);
            socket.send(JSON.stringify({
                type: 'RoomCreated',
                data: { roomId }
            }));
            gameServer.updateRoomStatus(roomId);
            break;

        case 'JoinRoom':
            const joinResult = gameServer.joinRoom(socket, data.roomId, data.nickname);
            socket.send(JSON.stringify({
                type: 'JoinResult',
                data: joinResult
            }));
            if (joinResult.success) {
                gameServer.updateRoomStatus(data.roomId);
            }
            break;

        case 'GetRoomList':
            socket.send(JSON.stringify({
                type: 'RoomList',
                data: { rooms: gameServer.getRoomList() }
            }));
            break;

        case 'Ready':
            handleReady(socket, data);
            break;

        case 'ChangeTeam':
            handleChangeTeam(socket, data);
            break;

        case 'StartGame':
            handleStartGame(socket, data);
            break;

        case 'Move':
            handleMove(socket, data);
            break;

        case 'Chat':
            handleChat(socket, data);
            break;

        case 'Surrender':
            handleSurrender(socket, data);
            break;

        case 'Ping':
            // 心跳响应
            socket.send(JSON.stringify({
                type: 'Pong',
                data: { timestamp: Date.now() }
            }));
            break;

        default:
            console.log(`[Server] 未知消息类型: ${type}`);
    }
}

// 处理准备状态
function handleReady(socket, data) {
    const clientInfo = gameServer.clients.get(socket);
    if (!clientInfo) return;

    const room = gameServer.rooms.get(clientInfo.roomId);
    if (!room) return;

    const player = room.players.get(socket);
    if (player && !player.isSpectator) {
        player.ready = !player.ready;
        gameServer.updateRoomStatus(clientInfo.roomId);
    }
}

// 处理队伍变更
function handleChangeTeam(socket, data) {
    const clientInfo = gameServer.clients.get(socket);
    if (!clientInfo) return;

    const room = gameServer.rooms.get(clientInfo.roomId);
    if (!room) return;

    const player = room.players.get(socket);
    if (player && !player.isSpectator) {
        player.team = data.team;
        gameServer.updateRoomStatus(clientInfo.roomId);
    }
}

// 处理开始游戏
function handleStartGame(socket, data) {
    const clientInfo = gameServer.clients.get(socket);
    if (!clientInfo) return;

    const room = gameServer.rooms.get(clientInfo.roomId);
    if (!room) return;

    const player = room.players.get(socket);
    if (!player || !player.isHost) return;

    // 检查所有玩家是否准备
    const players = Array.from(room.players.values()).filter(p => !p.isSpectator);
    const allReady = players.every(p => p.ready);

    if (players.length >= 2 && allReady) {
        startGame(clientInfo.roomId);
    }
}

// 开始游戏
function startGame(roomId) {
    const room = gameServer.rooms.get(roomId);
    if (!room) return;

    room.gameState = 'playing';
    room.startTime = Date.now();
    room.gameRound = 0;

    // 生成地图和初始化游戏状态
    const gameMap = generateGameMap(room);
    room.gameMap = gameMap;

    // 通知所有玩家游戏开始
    gameServer.broadcastToRoom(roomId, {
        type: 'GameStart',
        data: {
            gameMap: gameMap,
            gameMode: room.settings.gameMode
        }
    });

    // 启动游戏循环
    const gameTimer = setInterval(() => {
        gameLoop(roomId);
    }, 1000 / room.settings.gameSpeed);

    gameServer.roomTimers.set(roomId, gameTimer);
    console.log(`[Server] 房间 ${roomId} 游戏开始`);
}

// 游戏循环
function gameLoop(roomId) {
    const room = gameServer.rooms.get(roomId);
    if (!room || room.gameState !== 'playing') return;

    room.gameRound++;
    
    // 更新地图状态（军队增长等）
    updateGameMap(room);

    // 广播地图更新
    gameServer.broadcastToRoom(roomId, {
        type: 'MapUpdate',
        data: {
            mapDiff: getMapDiff(room.gameMap),
            round: room.gameRound
        }
    });

    // 检查游戏结束条件
    if (checkGameEnd(room)) {
        endGame(roomId);
    }
}

// 生成游戏地图
function generateGameMap(room) {
    const players = Array.from(room.players.values()).filter(p => !p.isSpectator);
    const width = 25;
    const height = 20;

    // 创建基础地图
    const map = Array(height).fill().map(() =>
        Array(width).fill().map(() => ({
            type: 'land', // land, general, city, mountain, swamp
            number: 0,
            belonging: 0,
            visible: false
        }))
    );

    // 生成山脉（约15%的地图）
    const mountainCount = Math.floor(width * height * 0.15);
    for (let i = 0; i < mountainCount; i++) {
        let x, y, attempts = 0;
        do {
            x = Math.floor(Math.random() * width);
            y = Math.floor(Math.random() * height);
            attempts++;
        } while (map[y][x].type !== 'land' && attempts < 100);

        if (attempts < 100) {
            map[y][x] = {
                type: 'mountain',
                number: 0,
                belonging: 0,
                visible: false
            };
        }
    }

    // 为每个玩家放置将军（确保分散）
    const playerPositions = [];
    players.forEach((player, index) => {
        let x, y, attempts = 0;
        let validPosition = false;

        do {
            x = Math.floor(Math.random() * width);
            y = Math.floor(Math.random() * height);
            attempts++;

            // 检查位置是否为陆地
            if (map[y][x].type !== 'land') continue;

            // 检查与其他玩家的距离
            validPosition = true;
            for (const pos of playerPositions) {
                const distance = Math.abs(x - pos.x) + Math.abs(y - pos.y);
                if (distance < 8) { // 最小距离8格
                    validPosition = false;
                    break;
                }
            }
        } while (!validPosition && attempts < 200);

        if (validPosition || attempts >= 200) {
            map[y][x] = {
                type: 'general',
                number: 1,
                belonging: player.id,
                visible: true
            };
            playerPositions.push({ x, y, playerId: player.id });
        }
    });

    // 放置城市（约3%的地图）
    const cityCount = Math.floor(width * height * 0.03);
    for (let i = 0; i < cityCount; i++) {
        let x, y, attempts = 0;
        do {
            x = Math.floor(Math.random() * width);
            y = Math.floor(Math.random() * height);
            attempts++;
        } while (map[y][x].type !== 'land' && attempts < 100);

        if (attempts < 100) {
            map[y][x] = {
                type: 'city',
                number: Math.floor(Math.random() * 30) + 20,
                belonging: 0,
                visible: false
            };
        }
    }

    // 放置沼泽（约2%的地图）
    const swampCount = Math.floor(width * height * 0.02);
    for (let i = 0; i < swampCount; i++) {
        let x, y, attempts = 0;
        do {
            x = Math.floor(Math.random() * width);
            y = Math.floor(Math.random() * height);
            attempts++;
        } while (map[y][x].type !== 'land' && attempts < 100);

        if (attempts < 100) {
            map[y][x] = {
                type: 'swamp',
                number: 0,
                belonging: 0,
                visible: false
            };
        }
    }

    return {
        width,
        height,
        cells: map,
        round: 0
    };
}

// 更新游戏地图
function updateGameMap(room) {
    const map = room.gameMap;
    
    // 每2回合，将军和城市增加1军队
    if (room.gameRound % 2 === 0) {
        for (let y = 0; y < map.height; y++) {
            for (let x = 0; x < map.width; x++) {
                const cell = map.cells[y][x];
                if ((cell.type === 'general' || cell.type === 'city') && cell.belonging > 0) {
                    cell.number++;
                }
            }
        }
    }

    // 每50回合，所有领土增加1军队
    if (room.gameRound % 50 === 0 && room.gameRound > 0) {
        for (let y = 0; y < map.height; y++) {
            for (let x = 0; x < map.width; x++) {
                const cell = map.cells[y][x];
                if (cell.belonging > 0) {
                    cell.number++;
                }
            }
        }
    }
}

// 获取地图差异（简化版本）
function getMapDiff(gameMap) {
    // 返回整个地图（实际应该只返回变化的部分）
    return gameMap.cells;
}

// 获取可见地图（根据玩家视野）
function getVisibleMap(gameMap, playerId) {
    const visibleCells = [];

    for (let y = 0; y < gameMap.height; y++) {
        visibleCells[y] = [];
        for (let x = 0; x < gameMap.width; x++) {
            const cell = gameMap.cells[y][x];

            // 检查是否在玩家视野内
            if (isVisibleToPlayer(gameMap, x, y, playerId)) {
                visibleCells[y][x] = { ...cell, visible: true };
            } else {
                // 隐藏单元格信息
                visibleCells[y][x] = {
                    type: cell.type === 'mountain' ? 'mountain' : 'unknown',
                    number: 0,
                    belonging: 0,
                    visible: false
                };
            }
        }
    }

    return visibleCells;
}

// 检查位置是否对玩家可见
function isVisibleToPlayer(gameMap, x, y, playerId) {
    // 检查周围8格是否有玩家的领土
    for (let dy = -1; dy <= 1; dy++) {
        for (let dx = -1; dx <= 1; dx++) {
            const nx = x + dx;
            const ny = y + dy;

            if (nx >= 0 && nx < gameMap.width && ny >= 0 && ny < gameMap.height) {
                const neighborCell = gameMap.cells[ny][nx];
                if (neighborCell.belonging === playerId) {
                    return true;
                }
            }
        }
    }

    return false;
}

// 检查游戏结束
function checkGameEnd(room) {
    const players = Array.from(room.players.values()).filter(p => !p.isSpectator);
    const alivePlayers = players.filter(p => {
        // 检查玩家是否还有将军
        for (let y = 0; y < room.gameMap.height; y++) {
            for (let x = 0; x < room.gameMap.width; x++) {
                const cell = room.gameMap.cells[y][x];
                if (cell.type === 'general' && cell.belonging === p.id) {
                    return true;
                }
            }
        }
        return false;
    });

    return alivePlayers.length <= 1;
}

// 结束游戏
function endGame(roomId) {
    const room = gameServer.rooms.get(roomId);
    if (!room) return;

    room.gameState = 'finished';
    
    // 清除游戏定时器
    if (gameServer.roomTimers.has(roomId)) {
        clearInterval(gameServer.roomTimers.get(roomId));
        gameServer.roomTimers.delete(roomId);
    }

    // 通知游戏结束
    gameServer.broadcastToRoom(roomId, {
        type: 'GameEnd',
        data: { winner: null } // 简化版本
    });

    console.log(`[Server] 房间 ${roomId} 游戏结束`);
}

// 处理移动
function handleMove(socket, data) {
    const clientInfo = gameServer.clients.get(socket);
    if (!clientInfo) return;

    const room = gameServer.rooms.get(clientInfo.roomId);
    if (!room || room.gameState !== 'playing') return;

    const player = room.players.get(socket);
    if (!player || player.isSpectator) return;

    const { path, halfTroops } = data;
    if (!path || path.length < 2) return;

    // 验证路径的每一步移动
    for (let i = 0; i < path.length - 1; i++) {
        const from = path[i];
        const to = path[i + 1];

        if (!isValidMove(room.gameMap, from.x, from.y, to.x, to.y, player.id)) {
            console.log(`[Server] 无效移动: ${from.x},${from.y} -> ${to.x},${to.y}`);
            return;
        }
    }

    // 执行移动
    executeMove(room, path, player.id, halfTroops);

    // 广播地图更新
    gameServer.broadcastToRoom(clientInfo.roomId, {
        type: 'MapUpdate',
        data: {
            mapDiff: getMapDiff(room.gameMap),
            round: room.gameRound
        }
    });

    console.log(`[Server] ${player.nickname} 执行移动`);
}

// 检查移动是否有效
function isValidMove(gameMap, fromX, fromY, toX, toY, playerId) {
    // 检查位置有效性
    if (fromX < 0 || fromX >= gameMap.width || fromY < 0 || fromY >= gameMap.height) return false;
    if (toX < 0 || toX >= gameMap.width || toY < 0 || toY >= gameMap.height) return false;

    // 检查是否相邻
    const dx = Math.abs(toX - fromX);
    const dy = Math.abs(toY - fromY);
    if (dx + dy !== 1) return false;

    const fromCell = gameMap.cells[fromY][fromX];
    const toCell = gameMap.cells[toY][toX];

    // 检查起始位置是否属于玩家
    if (fromCell.belonging !== playerId) return false;

    // 检查目标位置是否可移动（不能是山脉）
    if (toCell.type === 'mountain') return false;

    // 检查是否有足够的军队
    if (fromCell.number <= 1) return false;

    return true;
}

// 执行移动
function executeMove(room, path, playerId, halfTroops = false) {
    const gameMap = room.gameMap;

    for (let i = 0; i < path.length - 1; i++) {
        const from = path[i];
        const to = path[i + 1];

        const fromCell = gameMap.cells[from.y][from.x];
        const toCell = gameMap.cells[to.y][to.x];

        // 计算移动的军队数量
        let moveCount = fromCell.number - 1;
        if (halfTroops) {
            moveCount = Math.floor(moveCount / 2);
        }

        if (moveCount <= 0) continue;

        // 如果目标是敌方领土，进行战斗
        if (toCell.belonging !== 0 && toCell.belonging !== playerId) {
            if (moveCount > toCell.number) {
                // 攻击成功
                toCell.belonging = playerId;
                toCell.number = moveCount - toCell.number;
                fromCell.number -= moveCount;
            } else {
                // 攻击失败
                toCell.number -= moveCount;
                fromCell.number -= moveCount;
            }
        } else if (toCell.belonging === playerId) {
            // 移动到自己的领土
            toCell.number += moveCount;
            fromCell.number -= moveCount;
        } else {
            // 占领中性领土
            toCell.belonging = playerId;
            toCell.number = moveCount;
            fromCell.number -= moveCount;
        }
    }
}

// 处理聊天
function handleChat(socket, data) {
    const clientInfo = gameServer.clients.get(socket);
    if (!clientInfo) return;

    gameServer.broadcastToRoom(clientInfo.roomId, {
        type: 'Chat',
        data: {
            nickname: clientInfo.nickname,
            message: data.message
        }
    });
}

// 处理投降
function handleSurrender(socket, data) {
    console.log('[Server] 处理投降:', data);
}

// 路由
app.get('/', (req, res) => {
    res.redirect('/lobby');
});

app.get('/lobby', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'lobby.html'));
});

app.get('/room/:roomId', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'room.html'));
});

app.get('/game/:roomId', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'game.html'));
});

// 启动服务器
const PORT = process.env.PORT || 8080;

console.log('[Server] 正在启动服务器...');

server.listen(PORT, '0.0.0.0', () => {
    console.log(`[Server] Generals.io 网页版服务器启动在端口 ${PORT}`);
    console.log(`[Server] 访问 http://localhost:${PORT}/lobby 开始游戏`);
    console.log(`[Server] 服务器已准备就绪`);
});

server.on('error', (err) => {
    console.error('[Server] 服务器启动错误:', err);
});
