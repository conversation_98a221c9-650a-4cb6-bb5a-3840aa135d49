/* 房间页面样式 */
.room-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    padding: 20px;
}

.room-header {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    text-align: center;
}

.room-id {
    font-family: 'Quicksand Bold', sans-serif;
    font-size: 24px;
    color: #81d4fa;
    margin-bottom: 10px;
}

.room-status {
    font-size: 16px;
    color: rgba(129, 212, 250, 0.8);
}

.room-content {
    display: flex;
    gap: 20px;
    flex: 1;
}

.room-settings {
    flex: 1;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.settings-section {
    margin-bottom: 30px;
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-title {
    font-family: 'Quicksand Bold', sans-serif;
    font-size: 18px;
    color: #81d4fa;
    margin-bottom: 15px;
    text-align: center;
}

.settings-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.settings-label {
    font-family: 'Quicksand Medium', sans-serif;
    font-size: 16px;
    color: #81d4fa;
}

.players-panel {
    width: 300px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.players-title {
    font-family: 'Quicksand Bold', sans-serif;
    font-size: 18px;
    color: #81d4fa;
    margin-bottom: 20px;
    text-align: center;
}

.player-list {
    margin-bottom: 20px;
}

.player-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.player-info {
    flex: 1;
}

.player-name {
    font-family: 'Quicksand Medium', sans-serif;
    font-size: 14px;
    color: #81d4fa;
    margin-bottom: 4px;
}

.player-team {
    font-size: 12px;
    color: rgba(129, 212, 250, 0.7);
}

.player-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
    background: rgba(68, 138, 255, 0.3);
    color: #fff;
}

.player-status.ready {
    background: rgba(76, 175, 80, 0.6);
}

.player-status.not-ready {
    background: rgba(244, 67, 54, 0.6);
}

.team-selection {
    margin-bottom: 20px;
}

.team-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 15px;
}

.team-btn {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 6px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-height: 35px;
}

.team-btn.team-1 { background-color: #f07178; }
.team-btn.team-2 { background-color: #82aaff; }
.team-btn.team-3 { background-color: #c3e88d; }
.team-btn.team-4 { background-color: #008080; }
.team-btn.team-5 { background-color: #ffcb6b; }
.team-btn.team-6 { background-color: #c792ea; }
.team-btn.team-7 { background-color: #f78c6c; }
.team-btn.team-8 { background-color: #ff9800; }

.team-btn:hover {
    opacity: 0.8;
}

.team-btn.selected {
    border-color: #fff;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.room-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.room-actions .btn {
    flex: 1;
    font-size: 16px;
}

.back-btn {
    position: absolute;
    top: 20px;
    left: 20px;
    padding: 8px 16px;
    font-size: 14px;
    min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .room-content {
        flex-direction: column;
    }
    
    .players-panel {
        width: 100%;
    }
    
    .team-buttons {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .room-actions {
        flex-direction: column;
    }
    
    .back-btn {
        position: static;
        margin-bottom: 20px;
        align-self: flex-start;
    }
}
