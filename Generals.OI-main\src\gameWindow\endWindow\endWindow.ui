<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EndWindow</class>
 <widget class="QWidget" name="EndWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>492</width>
    <height>573</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <family>Quicksand Medium</family>
    <pointsize>18</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>EndWindow</string>
  </property>
  <property name="autoFillBackground">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="vlWnd">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>40</number>
   </property>
   <property name="topMargin">
    <number>40</number>
   </property>
   <property name="rightMargin">
    <number>40</number>
   </property>
   <property name="bottomMargin">
    <number>40</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="vlLabels">
     <property name="leftMargin">
      <number>0</number>
     </property>
     <item>
      <layout class="QHBoxLayout" name="hlGeneral">
       <item>
        <spacer name="hsGeneral1">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="lbGeneral">
         <property name="minimumSize">
          <size>
           <width>200</width>
           <height>160</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>200</width>
           <height>160</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="pixmap">
          <pixmap resource="../../../res/generals.qrc">:/img/General-Blue.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="hsGeneral2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="vsLabel1">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>30</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="lbTitle">
       <property name="font">
        <font>
         <family>Quicksand Medium</family>
         <pointsize>25</pointsize>
        </font>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="vsLabel2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="lbContent">
       <property name="font">
        <font>
         <family>Quicksand Regular</family>
         <pointsize>14</pointsize>
        </font>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="vsWnd">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>60</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="hlButtons">
     <item>
      <spacer name="hsButton1">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Expanding</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QVBoxLayout" name="vlButtons">
       <property name="spacing">
        <number>0</number>
       </property>
       <item>
        <widget class="QPushButton" name="pbWatch">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
           <horstretch>30</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Medium</family>
           <pointsize>20</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Watch</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="vsButton">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pbExit">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
           <horstretch>30</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Medium</family>
           <pointsize>20</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Exit</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="hsButton2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeType">
        <enum>QSizePolicy::Expanding</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../res/generals.qrc"/>
 </resources>
 <connections/>
</ui>
