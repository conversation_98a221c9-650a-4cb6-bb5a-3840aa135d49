<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>StartWindow</class>
 <widget class="QWidget" name="StartWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>700</width>
    <height>753</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>700</width>
    <height>0</height>
   </size>
  </property>
  <property name="font">
   <font>
    <family>Quicksand Regular</family>
    <pointsize>20</pointsize>
    <stylestrategy>PreferAntialias</stylestrategy>
   </font>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="hlImage">
     <property name="topMargin">
      <number>0</number>
     </property>
     <item>
      <spacer name="hsImage1">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QLabel" name="lbImage">
       <property name="minimumSize">
        <size>
         <width>200</width>
         <height>160</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>200</width>
         <height>160</height>
        </size>
       </property>
       <property name="sizeIncrement">
        <size>
         <width>200</width>
         <height>160</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="pixmap">
        <pixmap resource="../../res/generals.qrc">:/img/General-Blue.png</pixmap>
       </property>
       <property name="scaledContents">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="hsImage2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="vs3">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="hlInput">
     <property name="spacing">
      <number>20</number>
     </property>
     <item>
      <spacer name="hsInput1">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QVBoxLayout" name="vlInput" stretch="10,10,10,10">
       <property name="spacing">
        <number>20</number>
       </property>
       <property name="topMargin">
        <number>20</number>
       </property>
       <property name="bottomMargin">
        <number>20</number>
       </property>
       <item>
        <widget class="QLabel" name="lbNickName">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>10</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>350</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Bold</family>
           <pointsize>20</pointsize>
           <stylestrategy>PreferAntialias</stylestrategy>
          </font>
         </property>
         <property name="text">
          <string>Nickname</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="leNickName">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>10</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>350</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Medium</family>
           <pointsize>20</pointsize>
           <stylestrategy>PreferAntialias</stylestrategy>
          </font>
         </property>
         <property name="whatsThis">
          <string/>
         </property>
         <property name="inputMask">
          <string/>
         </property>
         <property name="maxLength">
          <number>15</number>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="placeholderText">
          <string>Anonymous</string>
         </property>
         <property name="clearButtonEnabled">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="lbServerAddress">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>10</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>350</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Bold</family>
           <pointsize>20</pointsize>
           <stylestrategy>PreferAntialias</stylestrategy>
          </font>
         </property>
         <property name="text">
          <string>Server Address</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="leServerAddress">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>10</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>350</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Medium</family>
           <pointsize>20</pointsize>
           <stylestrategy>PreferAntialias</stylestrategy>
          </font>
         </property>
         <property name="maxLength">
          <number>15</number>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="placeholderText">
          <string>localhost</string>
         </property>
         <property name="clearButtonEnabled">
          <bool>false</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="hsInput2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="vs1">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QVBoxLayout" name="vlTeam">
     <property name="spacing">
      <number>20</number>
     </property>
     <item>
      <widget class="QLabel" name="lbTeam">
       <property name="font">
        <font>
         <family>Quicksand Bold</family>
         <pointsize>20</pointsize>
         <stylestrategy>PreferAntialias</stylestrategy>
        </font>
       </property>
       <property name="text">
        <string>Team</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="vs2">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QVBoxLayout" name="vlBottom">
     <property name="spacing">
      <number>20</number>
     </property>
     <item>
      <widget class="QLabel" name="lbMessage">
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>80</height>
        </size>
       </property>
       <property name="font">
        <font>
         <family>Quicksand Regular</family>
         <pointsize>16</pointsize>
         <stylestrategy>PreferAntialias</stylestrategy>
        </font>
       </property>
       <property name="text">
        <string>[Disconnected]
</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="hlButton">
       <property name="spacing">
        <number>20</number>
       </property>
       <item>
        <spacer name="hsButton1">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pbConnect">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>30</horstretch>
           <verstretch>20</verstretch>
          </sizepolicy>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Regular</family>
           <pointsize>20</pointsize>
           <stylestrategy>PreferAntialias</stylestrategy>
          </font>
         </property>
         <property name="whatsThis">
          <string>Connect to game server</string>
         </property>
         <property name="text">
          <string>Connect</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="hsButton2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pbReady">
         <property name="enabled">
          <bool>false</bool>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>30</horstretch>
           <verstretch>20</verstretch>
          </sizepolicy>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Regular</family>
           <pointsize>20</pointsize>
           <stylestrategy>PreferAntialias</stylestrategy>
          </font>
         </property>
         <property name="whatsThis">
          <string>Ready to play</string>
         </property>
         <property name="text">
          <string>Ready</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="hsButton3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pbCreateServer">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>30</horstretch>
           <verstretch>20</verstretch>
          </sizepolicy>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Regular</family>
           <pointsize>20</pointsize>
           <stylestrategy>PreferAntialias</stylestrategy>
          </font>
         </property>
         <property name="whatsThis">
          <string>Create game server</string>
         </property>
         <property name="text">
          <string>Create</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="hsButton4">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../res/generals.qrc"/>
 </resources>
 <connections/>
</ui>
