// 简单的路由系统
class Router {
    constructor() {
        this.routes = {};
        this.currentRoute = null;
        this.init();
    }

    init() {
        // 监听浏览器前进后退
        window.addEventListener('popstate', () => {
            this.handleRoute();
        });

        // 初始路由
        this.handleRoute();
    }

    // 注册路由
    register(path, handler) {
        this.routes[path] = handler;
    }

    // 导航到指定路径
    navigate(path) {
        window.history.pushState({}, '', path);
        this.handleRoute();
    }

    // 处理当前路由
    handleRoute() {
        const path = window.location.pathname;
        this.currentRoute = path;

        // 匹配路由
        if (path === '/' || path === '/大厅') {
            this.loadLobby();
        } else if (path.startsWith('/room/')) {
            const roomId = path.split('/')[2];
            this.loadRoom(roomId);
        } else if (path.startsWith('/game/')) {
            const roomId = path.split('/')[2];
            this.loadGame(roomId);
        } else {
            // 默认跳转到大厅
            this.navigate('/大厅');
        }
    }

    // 加载大厅页面
    loadLobby() {
        const app = document.getElementById('app');
        app.innerHTML = `
            <link rel="stylesheet" href="css/lobby.css">
            <div class="lobby-container">
                <div class="lobby-header">
                    <img src="assets/img/General-Blue.png" alt="Generals.io" class="lobby-logo">
                    <h1 class="lobby-title">Generals.io</h1>
                    <p class="lobby-subtitle">网页版战略游戏</p>
                </div>

                <div class="lobby-form">
                    <div class="form-group">
                        <label class="label">昵称</label>
                        <input type="text" id="nickname" class="input" placeholder="Anonymous" maxlength="15">
                    </div>
                </div>

                <div class="lobby-actions">
                    <button class="btn" id="create-room-btn">创建房间</button>
                    <button class="btn" id="refresh-rooms-btn">刷新房间</button>
                </div>

                <div class="join-by-id-container">
                    <div class="join-by-id-header">加入房间</div>
                    <div class="join-by-id-form">
                        <div class="form-group">
                            <label class="label">房间号</label>
                            <input type="text" id="room-id-input" class="input" placeholder="1234" maxlength="4" pattern="[0-9]{4}">
                        </div>
                        <button class="btn" id="join-room-btn">加入</button>
                    </div>
                </div>

                <div class="room-list-container">
                    <div class="room-list-header">房间列表</div>
                    <div class="room-list" id="room-list">
                        <div class="empty-room-list">暂无房间</div>
                    </div>
                </div>
            </div>
        `;

        this.initLobbyEvents();
    }

    // 加载房间页面
    loadRoom(roomId) {
        const app = document.getElementById('app');
        app.innerHTML = `
            <link rel="stylesheet" href="css/room.css">
            <div class="room-container">
                <button class="btn back-btn" id="back-to-lobby">返回大厅</button>
                
                <div class="room-header">
                    <div class="room-id">房间 ${roomId}</div>
                    <div class="room-status" id="room-status">等待玩家加入...</div>
                </div>

                <div class="room-content">
                    <div class="room-settings">
                        <div class="settings-section">
                            <div class="settings-title">视野类型</div>
                            <div class="settings-group">
                                <div class="radio-group">
                                    <label class="radio-item">
                                        <input type="radio" name="viewType" value="nearsighted" checked>
                                        <span>近视</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="viewType" value="mistyVeil">
                                        <span>迷雾</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="viewType" value="crystalClear">
                                        <span>透明</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <div class="settings-title">游戏速度</div>
                            <div class="settings-group">
                                <div class="radio-group">
                                    <label class="radio-item">
                                        <input type="radio" name="gameSpeed" value="1" checked>
                                        <span>1x</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="gameSpeed" value="1.5">
                                        <span>1.5x</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="gameSpeed" value="2">
                                        <span>2x</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="gameSpeed" value="3">
                                        <span>3x</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="gameSpeed" value="5">
                                        <span>5x</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="gameSpeed" value="10">
                                        <span>10x</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <div class="settings-title">修改器</div>
                            <div class="settings-group">
                                <div class="radio-group">
                                    <label class="checkbox-item">
                                        <input type="checkbox" name="leapfrog">
                                        <span>跳跃</span>
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" name="cityState">
                                        <span>城邦</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="settings-section">
                            <div class="settings-title">组队</div>
                            <div class="settings-group">
                                <div class="radio-group">
                                    <label class="radio-item">
                                        <input type="radio" name="teaming" value="enabled" checked>
                                        <span>启用</span>
                                    </label>
                                    <label class="radio-item">
                                        <input type="radio" name="teaming" value="disabled">
                                        <span>禁用</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="players-panel">
                        <div class="players-title" id="players-title">玩家 (1/8)</div>
                        <div class="player-list" id="player-list">
                            <!-- 默认显示当前玩家 -->
                        </div>

                        <div class="team-selection">
                            <div class="label">选择队伍</div>
                            <div class="team-buttons" id="team-buttons">
                                <button class="team-btn team-1" data-team="1">队伍 1</button>
                                <button class="team-btn team-2" data-team="2">队伍 2</button>
                                <button class="team-btn team-3" data-team="3">队伍 3</button>
                                <button class="team-btn team-4" data-team="4">队伍 4</button>
                                <button class="team-btn team-5" data-team="5">队伍 5</button>
                                <button class="team-btn team-6" data-team="6">队伍 6</button>
                                <button class="team-btn team-7" data-team="7">队伍 7</button>
                                <button class="team-btn team-8" data-team="8">队伍 8</button>
                            </div>
                        </div>

                        <div class="room-actions">
                            <button class="btn" id="ready-btn">准备</button>
                            <button class="btn" id="start-game-btn" disabled>开始游戏</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.initRoomEvents(roomId);
    }

    // 加载游戏页面
    loadGame(roomId) {
        const app = document.getElementById('app');
        app.innerHTML = `
            <link rel="stylesheet" href="css/game.css">
            <div class="game-container">
                <div class="game-main">
                    <div class="game-header">
                        <div class="game-info">
                            <div class="round-info" id="round-info">回合: 1</div>
                            <div class="room-info">房间: ${roomId}</div>
                        </div>
                        <div class="game-controls">
                            <button class="btn" id="surrender-btn">投降</button>
                            <button class="btn" id="leave-game-btn">离开游戏</button>
                        </div>
                    </div>
                    
                    <div class="game-board">
                        <div class="map-container" id="map-container">
                            <!-- 游戏地图将在这里动态生成 -->
                        </div>
                    </div>
                </div>

                <div class="game-sidebar">
                    <div class="leaderboard">
                        <div class="leaderboard-title">排行榜</div>
                        <div id="leaderboard-list">
                            <!-- 排行榜将在这里动态生成 -->
                        </div>
                    </div>

                    <div class="chat-container">
                        <div class="chat-messages" id="chat-messages">
                            <!-- 聊天消息将在这里显示 -->
                        </div>
                        <div class="chat-input-container">
                            <input type="text" class="chat-input" id="chat-input" placeholder="输入消息..." maxlength="100">
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.initGameEvents(roomId);
    }

    // 初始化大厅事件
    initLobbyEvents() {
        // 创建房间
        document.getElementById('create-room-btn').addEventListener('click', () => {
            const nickname = document.getElementById('nickname').value.trim() || 'Anonymous';
            // 生成4位房间号
            const roomId = Math.floor(1000 + Math.random() * 9000).toString();
            
            // 保存昵称
            localStorage.setItem('nickname', nickname);
            
            // 跳转到房间页面
            this.navigate(`/room/${roomId}`);
        });

        // 加入房间
        document.getElementById('join-room-btn').addEventListener('click', () => {
            const roomId = document.getElementById('room-id-input').value.trim();
            const nickname = document.getElementById('nickname').value.trim() || 'Anonymous';
            
            if (!/^\d{4}$/.test(roomId)) {
                alert('请输入4位数字房间号');
                return;
            }
            
            // 保存昵称
            localStorage.setItem('nickname', nickname);
            
            // 跳转到房间页面
            this.navigate(`/room/${roomId}`);
        });

        // 刷新房间列表
        document.getElementById('refresh-rooms-btn').addEventListener('click', () => {
            this.refreshRoomList();
        });

        // 加载保存的昵称
        const savedNickname = localStorage.getItem('nickname');
        if (savedNickname) {
            document.getElementById('nickname').value = savedNickname;
        }

        // 初始加载房间列表
        this.refreshRoomList();
    }

    // 初始化房间事件
    initRoomEvents(roomId) {
        // 返回大厅
        document.getElementById('back-to-lobby').addEventListener('click', () => {
            this.navigate('/大厅');
        });

        // 队伍选择
        document.querySelectorAll('.team-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.team-btn').forEach(b => b.classList.remove('selected'));
                btn.classList.add('selected');
            });
        });

        // 准备按钮
        document.getElementById('ready-btn').addEventListener('click', () => {
            const btn = document.getElementById('ready-btn');
            if (btn.textContent === '准备') {
                btn.textContent = '取消准备';
                btn.classList.add('ready');
            } else {
                btn.textContent = '准备';
                btn.classList.remove('ready');
            }
        });

        // 开始游戏
        document.getElementById('start-game-btn').addEventListener('click', () => {
            this.navigate(`/game/${roomId}`);
        });
    }

    // 初始化游戏事件
    initGameEvents(roomId) {
        // 离开游戏
        document.getElementById('leave-game-btn').addEventListener('click', () => {
            if (confirm('确定要离开游戏吗？')) {
                this.navigate('/大厅');
            }
        });

        // 投降
        document.getElementById('surrender-btn').addEventListener('click', () => {
            if (confirm('确定要投降吗？')) {
                // 处理投降逻辑
                console.log('Player surrendered');
            }
        });

        // 聊天输入
        const chatInput = document.getElementById('chat-input');
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const message = chatInput.value.trim();
                if (message) {
                    this.sendChatMessage(message);
                    chatInput.value = '';
                }
            }
        });
    }

    // 刷新房间列表
    refreshRoomList() {
        const roomList = document.getElementById('room-list');
        // 这里应该从服务器获取房间列表
        // 暂时显示示例数据
        roomList.innerHTML = '<div class="empty-room-list">暂无房间</div>';
    }

    // 发送聊天消息
    sendChatMessage(message) {
        const chatMessages = document.getElementById('chat-messages');
        const nickname = localStorage.getItem('nickname') || 'Anonymous';
        
        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message';
        messageElement.innerHTML = `<span class="sender">${nickname}:</span> ${message}`;
        
        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// 创建全局路由实例
window.router = new Router();
