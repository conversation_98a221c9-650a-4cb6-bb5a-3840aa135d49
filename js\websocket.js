// WebSocket 通信管理
class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.messageHandlers = {};
        this.serverAddress = 'localhost';
        this.serverPort = 32767;
    }

    // 连接到服务器
    connect(address = this.serverAddress) {
        this.serverAddress = address;
        const wsUrl = `ws://${this.serverAddress}:${this.serverPort}`;
        
        try {
            this.socket = new WebSocket(wsUrl);
            this.setupEventHandlers();
        } catch (error) {
            console.error('WebSocket connection failed:', error);
            this.handleConnectionError();
        }
    }

    // 设置WebSocket事件处理器
    setupEventHandlers() {
        this.socket.onopen = () => {
            console.log('WebSocket connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.onConnectionStatusChange(true);
        };

        this.socket.onclose = (event) => {
            console.log('WebSocket disconnected:', event.code, event.reason);
            this.isConnected = false;
            this.onConnectionStatusChange(false);
            
            if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
                this.attemptReconnect();
            }
        };

        this.socket.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.handleConnectionError();
        };

        this.socket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('Failed to parse message:', error);
            }
        };
    }

    // 处理连接错误
    handleConnectionError() {
        this.isConnected = false;
        this.onConnectionStatusChange(false);
    }

    // 尝试重连
    attemptReconnect() {
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        
        setTimeout(() => {
            this.connect();
        }, this.reconnectDelay * this.reconnectAttempts);
    }

    // 断开连接
    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        this.isConnected = false;
    }

    // 发送消息
    send(messageType, data = []) {
        if (!this.isConnected || !this.socket) {
            console.warn('WebSocket not connected, cannot send message');
            return false;
        }

        try {
            const message = JSON.stringify([messageType, data]);
            this.socket.send(message);
            console.log('Sent message:', messageType, data);
            return true;
        } catch (error) {
            console.error('Failed to send message:', error);
            return false;
        }
    }

    // 处理接收到的消息
    handleMessage(data) {
        if (!Array.isArray(data) || data.length < 2) {
            console.warn('Invalid message format:', data);
            return;
        }

        const [messageType, messageData] = data;
        console.log('Received message:', messageType, messageData);

        // 调用注册的消息处理器
        if (this.messageHandlers[messageType]) {
            this.messageHandlers[messageType](messageData);
        } else {
            console.warn('No handler for message type:', messageType);
        }
    }

    // 注册消息处理器
    onMessage(messageType, handler) {
        this.messageHandlers[messageType] = handler;
    }

    // 移除消息处理器
    offMessage(messageType) {
        delete this.messageHandlers[messageType];
    }

    // 连接状态变化回调
    onConnectionStatusChange(isConnected) {
        // 更新UI状态
        const statusElements = document.querySelectorAll('.connection-status');
        statusElements.forEach(element => {
            element.textContent = isConnected ? '已连接' : '未连接';
            element.className = `connection-status ${isConnected ? 'connected' : 'disconnected'}`;
        });

        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('connectionStatusChanged', {
            detail: { isConnected }
        }));
    }

    // 发送昵称设置
    setNickname(nickname) {
        return this.send('SetNickname', [nickname]);
    }

    // 发送准备状态
    setReady() {
        return this.send('Readied', []);
    }

    // 发送游戏移动
    sendMove(startX, startY, deltaX, deltaY, isHalf = false) {
        return this.send('Move', [startX, startY, deltaX, deltaY, isHalf]);
    }

    // 发送聊天消息
    sendChat(message) {
        const nickname = localStorage.getItem('nickname') || 'Anonymous';
        return this.send('Chat', [nickname, message]);
    }

    // 发送投降
    surrender() {
        return this.send('Surrender', []);
    }

    // 请求加入房间
    joinRoom(roomId) {
        return this.send('JoinRoom', [roomId]);
    }

    // 请求创建房间
    createRoom(settings) {
        return this.send('CreateRoom', [settings]);
    }

    // 请求房间列表
    requestRoomList() {
        return this.send('RequestRoomList', []);
    }

    // 更新游戏设置
    updateGameSettings(settings) {
        return this.send('UpdateSettings', [settings]);
    }

    // 选择队伍
    selectTeam(teamId) {
        return this.send('SelectTeam', [teamId]);
    }

    // 开始游戏
    startGame() {
        return this.send('StartGame', []);
    }
}

// 创建全局WebSocket管理器实例
window.wsManager = new WebSocketManager();

// 设置基本的消息处理器
window.wsManager.onMessage('PlayerInfo', (data) => {
    console.log('Player info received:', data);
    // 处理玩家信息
});

window.wsManager.onMessage('PlayersInfo', (data) => {
    console.log('Players info received:', data);
    // 处理所有玩家信息
});

window.wsManager.onMessage('InitGame', (data) => {
    console.log('Game initialization received:', data);
    // 初始化游戏
    if (window.gameManager) {
        window.gameManager.initGame(data);
    }
});

window.wsManager.onMessage('UpdateMap', (data) => {
    console.log('Map update received:', data);
    // 更新地图
    if (window.gameManager) {
        window.gameManager.updateMap(data);
    }
});

window.wsManager.onMessage('Chat', (data) => {
    console.log('Chat message received:', data);
    // 处理聊天消息
    if (data.length >= 2) {
        const [sender, message] = data;
        window.addChatMessage(sender, message);
    }
});

window.wsManager.onMessage('GameOver', (data) => {
    console.log('Game over received:', data);
    // 处理游戏结束
    if (window.gameManager) {
        window.gameManager.handleGameOver(data);
    }
});

window.wsManager.onMessage('RoomList', (data) => {
    console.log('Room list received:', data);
    // 更新房间列表
    window.updateRoomList(data);
});

window.wsManager.onMessage('RoomUpdate', (data) => {
    console.log('Room update received:', data);
    // 更新房间状态
    window.updateRoomStatus(data);
});

// 全局聊天消息处理函数
window.addChatMessage = function(sender, message) {
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message';
        messageElement.innerHTML = `<span class="sender">${sender}:</span> ${message}`;
        
        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
};

// 全局房间列表更新函数
window.updateRoomList = function(rooms) {
    const roomList = document.getElementById('room-list');
    if (roomList) {
        if (rooms.length === 0) {
            roomList.innerHTML = '<div class="empty-room-list">暂无房间</div>';
        } else {
            roomList.innerHTML = rooms.map(room => `
                <div class="room-item">
                    <div class="room-info">
                        <div class="room-name">房间 ${room.id}</div>
                        <div class="room-details">速度: ${room.speed}x | 模式: ${room.mode}</div>
                    </div>
                    <div class="room-players">${room.players}/${room.maxPlayers}</div>
                    <button class="btn join-room-btn" onclick="joinRoom('${room.id}')">加入</button>
                </div>
            `).join('');
        }
    }
};

// 全局房间状态更新函数
window.updateRoomStatus = function(roomData) {
    // 更新房间页面的状态
    const roomStatus = document.getElementById('room-status');
    if (roomStatus) {
        roomStatus.textContent = roomData.status || '等待玩家加入...';
    }

    // 更新玩家列表
    const playerList = document.getElementById('player-list');
    if (playerList && roomData.players) {
        playerList.innerHTML = roomData.players.map(player => `
            <div class="player-item">
                <div class="player-info">
                    <div class="player-name">${player.name}</div>
                    <div class="player-team">队伍 ${player.team}</div>
                </div>
                <div class="player-status ${player.ready ? 'ready' : 'not-ready'}">
                    ${player.ready ? '准备' : '未准备'}
                </div>
            </div>
        `).join('');
    }
};

// 全局加入房间函数
window.joinRoom = function(roomId) {
    const nickname = localStorage.getItem('nickname') || 'Anonymous';
    window.wsManager.setNickname(nickname);
    window.wsManager.joinRoom(roomId);
    window.router.navigate(`/room/${roomId}`);
};
