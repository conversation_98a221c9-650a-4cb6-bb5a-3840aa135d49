QLineEdit {
    background-color: rgba(0, 0, 0, 20%);
    color: #b5b6e3;
    selection-color: #b5b6e3;
    selection-background-color: rgba(255, 255, 255, 20%);
    border: 0 solid #3574f0;
    border-bottom-width: 1px;
    font-family: "Quicksand Medium";
    qproperty-clearButtonEnabled: false;
}

QLabel {
    background-color: transparent;
    color: #81d4fa;
    qproperty-alignment: AlignCenter;
}

QCheckBox {
    color: #81d4fa;
    outline: 0;
}

.QCheckBox::indicator {
    width: 16px;
    height: 16px;
    image: url(":/img/CheckBox.png");
}

.QCheckBox::indicator:checked {
    width: 16px;
    height: 16px;
    image: url(":/img/CheckBox-Checked.png");
}

.QRadioButton {
    color: #81d4fa;
    outline: 0;
}

.QRadioButton:disabled, .QCheckBox:disabled {
    color: rgba(129, 212, 250, 50%);
}

.QRadioButton::indicator {
    width: 16px;
    height: 16px;
    image: url(":/img/RadioButton.png");
}

.QRadioButton::indicator:checked {
    image: url(":/img/RadioButton-Checked.png");
}

QPushButton {
    background-color: rgba(68, 138, 255, 80%);
    color: rgba(255, 255, 255, 100%);
    font-family: "Quicksand Regular";
    text-align: center;
}

QPushButton:hover {
    background-color: rgba(68, 138, 255, 85%);
}

QPushButton:pressed {
    background-color: rgba(68, 138, 255, 75%);
}

.QPushButton:disabled {
    background-color: rgba(68, 138, 255, 50%);
    color: rgba(255, 255, 255, 127);
}

TeamButton {
    font-size: 14px;
}

#CloseButton {
    border-image: url(":/img/CloseButton.png");
    background-color: transparent;
    qproperty-focusPolicy: NoFocus;
}

#CloseButton:hover {
    border-image: url(":/img/CloseButton-Hover.png");
}

#CloseButton:pressed {
    border-image: url(":/img/CloseButton-Clicked.png");
}

#MinimizeButton {
    border-image: url(":/img/MinimizeButton.png");
    background-color: transparent;
    qproperty-focusPolicy: NoFocus;
}

#MinimizeButton:hover {
    border-image: url(":/img/MinimizeButton-Hover.png");
}

#MinimizeButton:pressed {
    border-image: url(":/img/MinimizeButton-Clicked.png");
}
