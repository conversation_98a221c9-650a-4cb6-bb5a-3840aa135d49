/* 大厅页面样式 */
.lobby-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

.lobby-header {
    text-align: center;
    margin-bottom: 40px;
}

.lobby-logo {
    width: 200px;
    height: 160px;
    margin-bottom: 20px;
}

.lobby-title {
    font-family: 'Quicksand Bold', sans-serif;
    font-size: 32px;
    color: #81d4fa;
    margin-bottom: 10px;
}

.lobby-subtitle {
    font-family: 'Quicksand Regular', sans-serif;
    font-size: 18px;
    color: rgba(129, 212, 250, 0.8);
}

.lobby-form {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    min-width: 400px;
    backdrop-filter: blur(10px);
}

.form-group {
    margin-bottom: 25px;
}

.form-group:last-child {
    margin-bottom: 0;
}

.lobby-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.lobby-actions .btn {
    min-width: 120px;
    font-size: 18px;
}

.room-list-container {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-top: 30px;
    min-width: 600px;
    backdrop-filter: blur(10px);
}

.room-list-header {
    font-family: 'Quicksand Bold', sans-serif;
    font-size: 20px;
    color: #81d4fa;
    text-align: center;
    margin-bottom: 20px;
}

.room-list {
    max-height: 300px;
    overflow-y: auto;
}

.room-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.room-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.room-info {
    flex: 1;
}

.room-name {
    font-family: 'Quicksand Medium', sans-serif;
    font-size: 16px;
    color: #81d4fa;
    margin-bottom: 5px;
}

.room-details {
    font-size: 14px;
    color: rgba(129, 212, 250, 0.7);
}

.room-players {
    margin-right: 15px;
    font-size: 14px;
    color: rgba(129, 212, 250, 0.8);
}

.join-room-btn {
    padding: 8px 16px;
    font-size: 14px;
    min-width: 80px;
    min-height: 35px;
}

.empty-room-list {
    text-align: center;
    color: rgba(129, 212, 250, 0.6);
    font-style: italic;
    padding: 40px 20px;
}

.join-by-id-container {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;
    min-width: 400px;
    backdrop-filter: blur(10px);
}

.join-by-id-header {
    font-family: 'Quicksand Bold', sans-serif;
    font-size: 18px;
    color: #81d4fa;
    text-align: center;
    margin-bottom: 15px;
}

.join-by-id-form {
    display: flex;
    gap: 15px;
    align-items: end;
}

.join-by-id-form .form-group {
    flex: 1;
    margin-bottom: 0;
}

.join-by-id-form .btn {
    min-width: 100px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .lobby-form,
    .room-list-container,
    .join-by-id-container {
        min-width: auto;
        width: 100%;
        max-width: 400px;
    }
    
    .lobby-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .lobby-actions .btn {
        width: 100%;
        max-width: 200px;
    }
    
    .room-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .room-players {
        margin-right: 0;
    }
    
    .join-by-id-form {
        flex-direction: column;
        gap: 15px;
    }
    
    .join-by-id-form .btn {
        width: 100%;
    }
}
