#!/usr/bin/env python3
"""
简单的HTTP服务器，用于测试网页版Generals.io
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # 解析URL路径
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # 处理路由
        if path == '/' or path == '/大厅':
            # 返回主页面
            self.path = '/index.html'
        elif path.startswith('/room/') or path.startswith('/game/'):
            # 房间和游戏页面也返回主页面，让前端路由处理
            self.path = '/index.html'
        
        # 调用父类方法处理请求
        return super().do_GET()
    
    def end_headers(self):
        # 添加CORS头部，允许跨域请求
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def main():
    # 设置端口
    PORT = 8000
    
    # 检查是否指定了端口
    if len(sys.argv) > 1:
        try:
            PORT = int(sys.argv[1])
        except ValueError:
            print("Invalid port number. Using default port 8000.")
    
    # 创建服务器
    with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
        print(f"Serving at http://localhost:{PORT}")
        print(f"访问 http://localhost:{PORT}/大厅 开始游戏")
        print("按 Ctrl+C 停止服务器")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n服务器已停止")

if __name__ == "__main__":
    main()
