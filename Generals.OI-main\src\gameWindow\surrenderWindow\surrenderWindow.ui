<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SurrenderWindow</class>
 <widget class="QWidget" name="SurrenderWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>480</width>
    <height>338</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <family>Quicksand Medium</family>
    <pointsize>12</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>surrenderWindow</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="leftMargin">
    <number>30</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>30</number>
   </property>
   <item>
    <spacer name="vs1">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>10</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QLabel" name="lbSurrender">
     <property name="font">
      <font>
       <family>Quicksand Medium</family>
       <pointsize>23</pointsize>
      </font>
     </property>
     <property name="text">
      <string>Confirm Surrender ?</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="vs2">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>50</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="hlButtons">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <spacer name="hs1">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>10</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QVBoxLayout" name="vlButtons">
       <property name="spacing">
        <number>20</number>
       </property>
       <item>
        <widget class="QPushButton" name="pbYes">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
           <horstretch>50</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Medium</family>
           <pointsize>23</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Yes</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pbNo">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
           <horstretch>50</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="font">
          <font>
           <family>Quicksand Medium</family>
           <pointsize>23</pointsize>
          </font>
         </property>
         <property name="text">
          <string>No</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="hs2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>10</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>10</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
