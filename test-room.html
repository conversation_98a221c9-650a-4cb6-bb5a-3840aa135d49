<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>房间页面测试 - Generals.io</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/room.css">
    <link rel="icon" href="assets/img/Icon-Blue.png" type="image/png">
</head>
<body>
    <div class="room-container">
        <button class="btn back-btn" id="back-to-lobby">返回大厅</button>
        
        <div class="room-header">
            <div class="room-id">房间 1234</div>
            <div class="room-status" id="room-status">等待玩家加入...</div>
        </div>

        <div class="room-content">
            <div class="room-settings">
                <div class="settings-section">
                    <div class="settings-title">视野类型</div>
                    <div class="settings-group">
                        <div class="radio-group">
                            <label class="radio-item">
                                <input type="radio" name="viewType" value="nearsighted" checked>
                                <span>近视</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="viewType" value="mistyVeil">
                                <span>迷雾</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="viewType" value="crystalClear">
                                <span>透明</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="settings-title">游戏速度</div>
                    <div class="settings-group">
                        <div class="radio-group">
                            <label class="radio-item">
                                <input type="radio" name="gameSpeed" value="1" checked>
                                <span>1x</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="gameSpeed" value="1.5">
                                <span>1.5x</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="gameSpeed" value="2">
                                <span>2x</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="gameSpeed" value="3">
                                <span>3x</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="gameSpeed" value="5">
                                <span>5x</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="gameSpeed" value="10">
                                <span>10x</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="settings-title">修改器</div>
                    <div class="settings-group">
                        <div class="radio-group">
                            <label class="checkbox-item">
                                <input type="checkbox" name="leapfrog">
                                <span>跳跃</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="cityState">
                                <span>城邦</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="settings-title">组队</div>
                    <div class="settings-group">
                        <div class="radio-group">
                            <label class="radio-item">
                                <input type="radio" name="teaming" value="enabled" checked>
                                <span>启用</span>
                            </label>
                            <label class="radio-item">
                                <input type="radio" name="teaming" value="disabled">
                                <span>禁用</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="players-panel">
                <div class="players-title">玩家 (1/8)</div>
                <div class="player-list" id="player-list">
                    <div class="player-item">
                        <div class="player-info">
                            <div class="player-name">测试玩家</div>
                            <div class="player-team">队伍 1</div>
                        </div>
                        <div class="player-status ready">准备</div>
                    </div>
                </div>

                <div class="team-selection">
                    <div class="label">选择队伍</div>
                    <div class="team-buttons" id="team-buttons">
                        <button class="team-btn team-1 selected" data-team="1">队伍 1</button>
                        <button class="team-btn team-2" data-team="2">队伍 2</button>
                        <button class="team-btn team-3" data-team="3">队伍 3</button>
                        <button class="team-btn team-4" data-team="4">队伍 4</button>
                        <button class="team-btn team-5" data-team="5">队伍 5</button>
                        <button class="team-btn team-6" data-team="6">队伍 6</button>
                        <button class="team-btn team-7" data-team="7">队伍 7</button>
                        <button class="team-btn team-8" data-team="8">队伍 8</button>
                    </div>
                </div>

                <div class="room-actions">
                    <button class="btn ready" id="ready-btn">取消准备</button>
                    <button class="btn" id="start-game-btn" disabled>开始游戏</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互测试
        document.getElementById('back-to-lobby').addEventListener('click', () => {
            alert('返回大厅');
        });

        document.querySelectorAll('.team-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.team-btn').forEach(b => b.classList.remove('selected'));
                btn.classList.add('selected');
            });
        });

        document.getElementById('ready-btn').addEventListener('click', () => {
            const btn = document.getElementById('ready-btn');
            if (btn.textContent === '准备') {
                btn.textContent = '取消准备';
                btn.classList.add('ready');
            } else {
                btn.textContent = '准备';
                btn.classList.remove('ready');
            }
        });
    </script>
</body>
</html>
