// 游戏核心逻辑管理
class GameManager {
    constructor() {
        this.gameMap = null;
        this.playerInfo = null;
        this.playersInfo = {};
        this.currentRound = 1;
        this.gameMode = 0;
        this.isGameActive = false;
        this.selectedCell = null;
        this.movePath = [];
        this.focusPosition = { x: 0, y: 0 };
        this.mapSize = { width: 0, height: 0 };
        this.cellSize = 30;
        this.isHalfMove = false;
        
        this.initKeyboardControls();
    }

    // 初始化游戏
    initGame(gameData) {
        console.log('Initializing game with data:', gameData);
        
        if (gameData.length < 2) {
            console.error('Invalid game data format');
            return;
        }

        // 解析游戏数据
        const mapData = gameData.slice(0, -1);
        this.gameMode = gameData[gameData.length - 1];
        
        this.parseMapData(mapData);
        this.renderMap();
        this.isGameActive = true;
        
        // 聚焦到玩家的将军位置
        this.focusOnGeneral();
    }

    // 解析地图数据
    parseMapData(mapData) {
        if (mapData.length < 2) return;
        
        this.mapSize.width = mapData[0];
        this.mapSize.height = mapData[1];
        
        // 初始化地图
        this.gameMap = [];
        for (let x = 0; x <= this.mapSize.width; x++) {
            this.gameMap[x] = [];
            for (let y = 0; y <= this.mapSize.height; y++) {
                this.gameMap[x][y] = {
                    type: 0, // 0: land, 1: general, 2: city, 3: mountain, 4: swamp
                    number: 0,
                    belonging: 0,
                    visible: false
                };
            }
        }

        // 解析地图数据
        let dataIndex = 2;
        for (let x = 1; x <= this.mapSize.width; x++) {
            for (let y = 1; y <= this.mapSize.height; y++) {
                if (dataIndex < mapData.length) {
                    const cellData = mapData[dataIndex];
                    this.gameMap[x][y] = {
                        type: cellData & 0xF,
                        number: (cellData >> 4) & 0xFFF,
                        belonging: (cellData >> 16) & 0xF,
                        visible: ((cellData >> 20) & 0x1) === 1
                    };
                    dataIndex++;
                }
            }
        }
    }

    // 更新地图
    updateMap(updateData) {
        if (!this.gameMap || !updateData) return;
        
        // 处理地图差异更新
        for (let i = 0; i < updateData.length; i += 3) {
            if (i + 2 < updateData.length) {
                const x = updateData[i];
                const y = updateData[i + 1];
                const cellData = updateData[i + 2];
                
                if (x >= 1 && x <= this.mapSize.width && y >= 1 && y <= this.mapSize.height) {
                    this.gameMap[x][y] = {
                        type: cellData & 0xF,
                        number: (cellData >> 4) & 0xFFF,
                        belonging: (cellData >> 16) & 0xF,
                        visible: ((cellData >> 20) & 0x1) === 1
                    };
                }
            }
        }
        
        this.renderMap();
        this.updateLeaderboard();
        this.currentRound++;
        this.updateRoundInfo();
    }

    // 渲染地图
    renderMap() {
        const mapContainer = document.getElementById('map-container');
        if (!mapContainer || !this.gameMap) return;

        // 设置网格布局
        mapContainer.style.gridTemplateColumns = `repeat(${this.mapSize.height}, ${this.cellSize}px)`;
        mapContainer.innerHTML = '';

        // 渲染每个格子
        for (let x = 1; x <= this.mapSize.width; x++) {
            for (let y = 1; y <= this.mapSize.height; y++) {
                const cell = this.gameMap[x][y];
                const cellElement = this.createCellElement(x, y, cell);
                mapContainer.appendChild(cellElement);
            }
        }
    }

    // 创建格子元素
    createCellElement(x, y, cell) {
        const cellElement = document.createElement('div');
        cellElement.className = 'map-cell';
        cellElement.dataset.x = x;
        cellElement.dataset.y = y;
        
        // 设置格子类型样式
        const typeClasses = ['land', 'general', 'city', 'mountain', 'swamp'];
        if (cell.type < typeClasses.length) {
            cellElement.classList.add(typeClasses[cell.type]);
        }
        
        // 设置玩家颜色
        if (cell.belonging > 0) {
            cellElement.classList.add(`player-${cell.belonging}`);
        }
        
        // 设置可见性
        if (cell.visible) {
            cellElement.classList.add('visible');
        } else {
            cellElement.classList.add('fog');
        }
        
        // 显示数字
        if (cell.number > 0 && cell.visible) {
            cellElement.textContent = cell.number;
        }
        
        // 添加点击事件
        cellElement.addEventListener('click', () => {
            this.handleCellClick(x, y);
        });
        
        return cellElement;
    }

    // 处理格子点击
    handleCellClick(x, y) {
        if (!this.isGameActive) return;
        
        if (this.selectedCell) {
            // 如果已经选择了格子，尝试移动
            const deltaX = x - this.selectedCell.x;
            const deltaY = y - this.selectedCell.y;
            
            if (Math.abs(deltaX) + Math.abs(deltaY) === 1) {
                // 相邻格子，执行移动
                this.executeMove(this.selectedCell.x, this.selectedCell.y, deltaX, deltaY);
            }
            
            this.clearSelection();
        } else {
            // 选择格子
            this.selectCell(x, y);
        }
    }

    // 选择格子
    selectCell(x, y) {
        const cell = this.gameMap[x][y];
        if (!cell || !cell.visible || cell.belonging !== this.playerInfo?.id) {
            return;
        }
        
        this.selectedCell = { x, y };
        this.updateCellSelection();
    }

    // 清除选择
    clearSelection() {
        this.selectedCell = null;
        this.movePath = [];
        this.updateCellSelection();
    }

    // 更新格子选择状态
    updateCellSelection() {
        // 清除所有选择状态
        document.querySelectorAll('.map-cell').forEach(cell => {
            cell.classList.remove('selected', 'path');
        });
        
        // 设置当前选择
        if (this.selectedCell) {
            const cellElement = document.querySelector(`[data-x="${this.selectedCell.x}"][data-y="${this.selectedCell.y}"]`);
            if (cellElement) {
                cellElement.classList.add('selected');
            }
        }
        
        // 设置移动路径
        this.movePath.forEach(pos => {
            const cellElement = document.querySelector(`[data-x="${pos.x}"][data-y="${pos.y}"]`);
            if (cellElement) {
                cellElement.classList.add('path');
            }
        });
    }

    // 执行移动
    executeMove(startX, startY, deltaX, deltaY) {
        if (!window.wsManager.isConnected) {
            console.warn('Not connected to server');
            return;
        }
        
        window.wsManager.sendMove(startX, startY, deltaX, deltaY, this.isHalfMove);
    }

    // 聚焦到将军位置
    focusOnGeneral() {
        if (!this.gameMap || !this.playerInfo) return;
        
        // 查找玩家的将军
        for (let x = 1; x <= this.mapSize.width; x++) {
            for (let y = 1; y <= this.mapSize.height; y++) {
                const cell = this.gameMap[x][y];
                if (cell.type === 1 && cell.belonging === this.playerInfo.id) {
                    this.focusPosition = { x, y };
                    this.centerMapOnPosition(x, y);
                    return;
                }
            }
        }
    }

    // 将地图居中到指定位置
    centerMapOnPosition(x, y) {
        const mapContainer = document.getElementById('map-container');
        if (!mapContainer) return;
        
        // 这里可以添加地图滚动逻辑
        // 暂时不实现，因为当前地图较小
    }

    // 初始化键盘控制
    initKeyboardControls() {
        document.addEventListener('keydown', (event) => {
            if (!this.isGameActive) return;
            
            switch (event.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    this.moveFocus(0, -1);
                    event.preventDefault();
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    this.moveFocus(0, 1);
                    event.preventDefault();
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    this.moveFocus(-1, 0);
                    event.preventDefault();
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    this.moveFocus(1, 0);
                    event.preventDefault();
                    break;
                case 'q':
                case 'Q':
                    this.clearSelection();
                    event.preventDefault();
                    break;
                case 'e':
                case 'E':
                    this.cancelLastMove();
                    event.preventDefault();
                    break;
                case 'z':
                case 'Z':
                    this.isHalfMove = !this.isHalfMove;
                    this.updateHalfMoveIndicator();
                    event.preventDefault();
                    break;
                case 'g':
                case 'G':
                    this.focusOnGeneral();
                    event.preventDefault();
                    break;
                case 'Enter':
                    const chatInput = document.getElementById('chat-input');
                    if (chatInput && !chatInput.matches(':focus')) {
                        chatInput.focus();
                        event.preventDefault();
                    }
                    break;
            }
        });
    }

    // 移动焦点
    moveFocus(deltaX, deltaY) {
        const newX = Math.max(1, Math.min(this.mapSize.width, this.focusPosition.x + deltaX));
        const newY = Math.max(1, Math.min(this.mapSize.height, this.focusPosition.y + deltaY));
        
        this.focusPosition = { x: newX, y: newY };
        
        // 如果有选择的格子，尝试移动
        if (this.selectedCell) {
            const moveX = newX - this.selectedCell.x;
            const moveY = newY - this.selectedCell.y;
            
            if (Math.abs(moveX) + Math.abs(moveY) === 1) {
                this.executeMove(this.selectedCell.x, this.selectedCell.y, moveX, moveY);
                this.clearSelection();
            }
        } else {
            // 选择当前焦点位置的格子
            this.selectCell(newX, newY);
        }
    }

    // 取消最后一步移动
    cancelLastMove() {
        if (this.movePath.length > 0) {
            this.movePath.pop();
            this.updateCellSelection();
        }
    }

    // 更新半移动指示器
    updateHalfMoveIndicator() {
        // 可以在UI中显示半移动状态
        console.log('Half move:', this.isHalfMove);
    }

    // 更新排行榜
    updateLeaderboard() {
        const leaderboardList = document.getElementById('leaderboard-list');
        if (!leaderboardList) return;
        
        // 计算玩家统计
        const playerStats = this.calculatePlayerStats();
        
        // 按军队数量排序
        const sortedPlayers = Object.values(playerStats).sort((a, b) => b.army - a.army);
        
        leaderboardList.innerHTML = sortedPlayers.map(player => `
            <div class="player-stats ${player.id === this.playerInfo?.id ? 'current-player' : ''} ${player.eliminated ? 'eliminated' : ''}">
                <div class="player-color" style="background-color: ${this.getPlayerColor(player.id)}"></div>
                <div class="player-name">${player.name}</div>
                <div class="player-army">${player.army}</div>
                <div class="player-land">${player.land}</div>
            </div>
        `).join('');
    }

    // 计算玩家统计
    calculatePlayerStats() {
        const stats = {};
        
        if (!this.gameMap) return stats;
        
        // 初始化统计
        Object.values(this.playersInfo).forEach(player => {
            stats[player.id] = {
                id: player.id,
                name: player.name,
                army: 0,
                land: 0,
                eliminated: false
            };
        });
        
        // 统计地图数据
        for (let x = 1; x <= this.mapSize.width; x++) {
            for (let y = 1; y <= this.mapSize.height; y++) {
                const cell = this.gameMap[x][y];
                if (cell.belonging > 0 && stats[cell.belonging]) {
                    stats[cell.belonging].army += cell.number;
                    stats[cell.belonging].land++;
                }
            }
        }
        
        return stats;
    }

    // 获取玩家颜色
    getPlayerColor(playerId) {
        const colors = [
            '#dcdcdc', '#f07178', '#82aaff', '#c3e88d', '#008080',
            '#ffcb6b', '#c792ea', '#f78c6c', '#ff9800'
        ];
        return colors[playerId] || colors[0];
    }

    // 更新回合信息
    updateRoundInfo() {
        const roundInfo = document.getElementById('round-info');
        if (roundInfo) {
            roundInfo.textContent = `回合: ${this.currentRound}`;
        }
    }

    // 处理游戏结束
    handleGameOver(gameOverData) {
        this.isGameActive = false;
        
        // 显示游戏结束界面
        this.showGameOverModal(gameOverData);
    }

    // 显示游戏结束模态框
    showGameOverModal(gameOverData) {
        const overlay = document.createElement('div');
        overlay.className = 'game-over-overlay';
        overlay.innerHTML = `
            <div class="game-over-modal">
                <div class="game-over-title">游戏结束</div>
                <div class="game-over-result">${this.getGameOverMessage(gameOverData)}</div>
                <div class="game-over-actions">
                    <button class="btn" onclick="window.router.navigate('/大厅')">返回大厅</button>
                    <button class="btn" onclick="location.reload()">重新开始</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(overlay);
    }

    // 获取游戏结束消息
    getGameOverMessage(gameOverData) {
        // 根据游戏结束数据生成消息
        if (gameOverData && gameOverData.winner) {
            return `${gameOverData.winner} 获得胜利！`;
        }
        return '游戏结束';
    }

    // 设置玩家信息
    setPlayerInfo(playerInfo) {
        this.playerInfo = playerInfo;
    }

    // 设置所有玩家信息
    setPlayersInfo(playersInfo) {
        this.playersInfo = playersInfo;
    }
}

// 创建全局游戏管理器实例
window.gameManager = new GameManager();
